// DialysisCare - Machine Details Screen Component
// Detailed view of a specific machine and its maintenance history

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useApp } from '../context/AppContext.js';
import { useNotification } from '../context/NotificationContext.js';
import { 
    formatDate, 
    formatCurrency, 
    getDaysUntilMaintenance,
    getMaintenanceStatus,
    MAINTENANCE_PRIORITIES 
} from '../constants.js';

const MachineDetailsScreen = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { machines, selectedMachine, selectMachine, addMaintenanceLog } = useApp();
    const { showSuccess, showError } = useNotification();
    const [activeTab, setActiveTab] = useState('overview');
    const [showAddLogModal, setShowAddLogModal] = useState(false);

    // Find the machine
    const machine = machines.find(m => m.id === id) || selectedMachine;

    useEffect(() => {
        if (machine) {
            selectMachine(machine);
        }
    }, [machine, selectMachine]);

    if (!machine) {
        return (
            <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">🔍</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Machine not found</h3>
                <p className="text-gray-600 mb-4">
                    The requested machine could not be found.
                </p>
                <Link to="/" className="btn-primary">
                    Back to Dashboard
                </Link>
            </div>
        );
    }

    const maintenanceStatus = getMaintenanceStatus(machine.nextMaintenanceDate);
    const daysUntilMaintenance = getDaysUntilMaintenance(machine.nextMaintenanceDate);

    const handleAddMaintenanceLog = (logData) => {
        addMaintenanceLog(machine.id, logData);
        setShowAddLogModal(false);
        showSuccess('Maintenance log added successfully');
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center space-x-4">
                    <button
                        onClick={() => navigate(-1)}
                        className="btn-secondary"
                    >
                        ← Back
                    </button>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{machine.name}</h1>
                        <p className="text-gray-600">{machine.manufacturer} - {machine.model}</p>
                    </div>
                </div>
                
                <div className="mt-4 sm:mt-0 flex items-center space-x-3">
                    <button
                        onClick={() => setShowAddLogModal(true)}
                        className="btn-primary"
                    >
                        Add Maintenance Log
                    </button>
                </div>
            </div>

            {/* Status Card */}
            <div className="bg-white rounded-lg shadow p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="text-center">
                        <div className={`status-badge status-${machine.status} text-lg px-4 py-2 mx-auto`}>
                            {machine.status}
                        </div>
                        <p className="text-sm text-gray-600 mt-2">Current Status</p>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">{machine.location}</div>
                        <p className="text-sm text-gray-600 mt-2">Location</p>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {formatDate(machine.lastMaintenanceDate)}
                        </div>
                        <p className="text-sm text-gray-600 mt-2">Last Maintenance</p>
                    </div>
                    <div className="text-center">
                        <div className={`text-2xl font-bold ${
                            maintenanceStatus === 'overdue' ? 'text-red-600' :
                            maintenanceStatus === 'due-soon' ? 'text-orange-600' :
                            'text-gray-900'
                        }`}>
                            {formatDate(machine.nextMaintenanceDate)}
                        </div>
                        <p className="text-sm text-gray-600 mt-2">
                            Next Maintenance
                            {daysUntilMaintenance !== null && (
                                <span className="block">
                                    {daysUntilMaintenance < 0 
                                        ? `(${Math.abs(daysUntilMaintenance)} days overdue)`
                                        : `(${daysUntilMaintenance} days remaining)`
                                    }
                                </span>
                            )}
                        </p>
                    </div>
                </div>
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-lg shadow">
                <div className="border-b border-gray-200">
                    <nav className="flex space-x-8 px-6">
                        {[
                            { id: 'overview', label: 'Overview' },
                            { id: 'maintenance', label: 'Maintenance History' },
                            { id: 'specifications', label: 'Specifications' }
                        ].map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`
                                    py-4 px-1 border-b-2 font-medium text-sm transition-colors
                                    ${activeTab === tab.id
                                        ? 'border-medical-blue-500 text-medical-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }
                                `}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </nav>
                </div>

                <div className="p-6">
                    {activeTab === 'overview' && <OverviewTab machine={machine} />}
                    {activeTab === 'maintenance' && <MaintenanceTab machine={machine} />}
                    {activeTab === 'specifications' && <SpecificationsTab machine={machine} />}
                </div>
            </div>

            {/* Add Maintenance Log Modal */}
            {showAddLogModal && (
                <AddMaintenanceLogModal
                    machine={machine}
                    onSave={handleAddMaintenanceLog}
                    onCancel={() => setShowAddLogModal(false)}
                />
            )}
        </div>
    );
};

// Overview Tab Component
const OverviewTab = ({ machine }) => {
    const totalCost = machine.maintenanceLogs.reduce((sum, log) => sum + (log.cost || 0), 0);
    const totalHours = machine.maintenanceLogs.reduce((sum, log) => sum + (log.duration || 0), 0);
    const recentLogs = machine.maintenanceLogs.slice(0, 3);

    return (
        <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="stat-card">
                    <div className="stat-value text-green-600">{formatCurrency(totalCost)}</div>
                    <div className="stat-label">Total Maintenance Cost</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-blue-600">{totalHours.toFixed(1)}h</div>
                    <div className="stat-label">Total Maintenance Hours</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-purple-600">{machine.maintenanceLogs.length}</div>
                    <div className="stat-label">Maintenance Records</div>
                </div>
            </div>

            {/* Recent Maintenance */}
            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Maintenance</h3>
                {recentLogs.length > 0 ? (
                    <div className="space-y-3">
                        {recentLogs.map(log => (
                            <div key={log.id} className="border border-gray-200 rounded-lg p-4">
                                <div className="flex items-start justify-between">
                                    <div>
                                        <h4 className="font-medium text-gray-900">{log.description}</h4>
                                        <p className="text-sm text-gray-600 mt-1">
                                            {formatDate(log.date)} • {log.technician} • {log.duration}h
                                        </p>
                                    </div>
                                    <div className={`priority-badge priority-${log.priority}`}>
                                        {MAINTENANCE_PRIORITIES[log.priority]?.label || log.priority}
                                    </div>
                                </div>
                                {log.cost && (
                                    <p className="text-sm text-gray-900 mt-2 font-medium">
                                        Cost: {formatCurrency(log.cost)}
                                    </p>
                                )}
                            </div>
                        ))}
                    </div>
                ) : (
                    <p className="text-gray-600">No maintenance records found.</p>
                )}
            </div>
        </div>
    );
};

// Maintenance Tab Component
const MaintenanceTab = ({ machine }) => {
    const [sortBy, setSortBy] = useState('date');
    const [sortOrder, setSortOrder] = useState('desc');

    const sortedLogs = [...machine.maintenanceLogs].sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        if (sortBy === 'date') {
            aValue = new Date(aValue);
            bValue = new Date(bValue);
        }

        if (sortOrder === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });

    return (
        <div className="space-y-4">
            {/* Sort Controls */}
            <div className="flex items-center space-x-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sort by
                    </label>
                    <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className="input-field"
                    >
                        <option value="date">Date</option>
                        <option value="priority">Priority</option>
                        <option value="cost">Cost</option>
                        <option value="duration">Duration</option>
                    </select>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Order
                    </label>
                    <select
                        value={sortOrder}
                        onChange={(e) => setSortOrder(e.target.value)}
                        className="input-field"
                    >
                        <option value="desc">Newest First</option>
                        <option value="asc">Oldest First</option>
                    </select>
                </div>
            </div>

            {/* Maintenance Logs */}
            {sortedLogs.length > 0 ? (
                <div className="space-y-4">
                    {sortedLogs.map(log => (
                        <div key={log.id} className="maintenance-log-card">
                            <div className="flex items-start justify-between mb-3">
                                <div>
                                    <h4 className="font-medium text-gray-900">{log.description}</h4>
                                    <p className="text-sm text-gray-600 mt-1">
                                        {formatDate(log.date)} • {log.technician}
                                    </p>
                                </div>
                                <div className={`priority-badge priority-${log.priority}`}>
                                    {MAINTENANCE_PRIORITIES[log.priority]?.label || log.priority}
                                </div>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                    <span className="text-gray-600">Duration:</span>
                                    <span className="ml-2 font-medium">{log.duration}h</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Cost:</span>
                                    <span className="ml-2 font-medium">{formatCurrency(log.cost)}</span>
                                </div>
                                <div className="md:col-span-2">
                                    <span className="text-gray-600">Parts Used:</span>
                                    <span className="ml-2 font-medium">{log.partsUsed || 'None'}</span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <p className="text-gray-600 text-center py-8">No maintenance records found.</p>
            )}
        </div>
    );
};

// Specifications Tab Component
const SpecificationsTab = ({ machine }) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <div className="space-y-3">
                    <div className="info-item">
                        <span className="info-label">Name:</span>
                        <span className="info-value">{machine.name}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Manufacturer:</span>
                        <span className="info-value">{machine.manufacturer}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Model:</span>
                        <span className="info-value">{machine.model}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Serial Number:</span>
                        <span className="info-value">{machine.serialNumber}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Location:</span>
                        <span className="info-value">{machine.location}</span>
                    </div>
                </div>
            </div>

            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Maintenance Schedule</h3>
                <div className="space-y-3">
                    <div className="info-item">
                        <span className="info-label">Last Maintenance:</span>
                        <span className="info-value">{formatDate(machine.lastMaintenanceDate)}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Next Maintenance:</span>
                        <span className="info-value">{formatDate(machine.nextMaintenanceDate)}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Maintenance Interval:</span>
                        <span className="info-value">30 days</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

// Add Maintenance Log Modal Component
const AddMaintenanceLogModal = ({ machine, onSave, onCancel }) => {
    const [formData, setFormData] = useState({
        date: new Date().toISOString().split('T')[0],
        technician: '',
        description: '',
        duration: '',
        partsUsed: '',
        cost: '',
        priority: 'medium'
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        onSave({
            ...formData,
            duration: parseFloat(formData.duration) || 0,
            cost: parseFloat(formData.cost) || 0
        });
    };

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-screen overflow-y-auto">
                <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Add Maintenance Log for {machine.name}
                    </h3>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Date
                            </label>
                            <input
                                type="date"
                                name="date"
                                value={formData.date}
                                onChange={handleChange}
                                required
                                className="input-field"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Technician
                            </label>
                            <input
                                type="text"
                                name="technician"
                                value={formData.technician}
                                onChange={handleChange}
                                required
                                className="input-field"
                                placeholder="Enter technician name"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Description
                            </label>
                            <textarea
                                name="description"
                                value={formData.description}
                                onChange={handleChange}
                                required
                                rows={3}
                                className="input-field"
                                placeholder="Describe the maintenance work performed"
                            />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Duration (hours)
                                </label>
                                <input
                                    type="number"
                                    name="duration"
                                    value={formData.duration}
                                    onChange={handleChange}
                                    step="0.5"
                                    min="0"
                                    className="input-field"
                                    placeholder="0.0"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Cost ($)
                                </label>
                                <input
                                    type="number"
                                    name="cost"
                                    value={formData.cost}
                                    onChange={handleChange}
                                    step="0.01"
                                    min="0"
                                    className="input-field"
                                    placeholder="0.00"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Parts Used
                            </label>
                            <input
                                type="text"
                                name="partsUsed"
                                value={formData.partsUsed}
                                onChange={handleChange}
                                className="input-field"
                                placeholder="List parts used (optional)"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Priority
                            </label>
                            <select
                                name="priority"
                                value={formData.priority}
                                onChange={handleChange}
                                className="input-field"
                            >
                                {Object.entries(MAINTENANCE_PRIORITIES).map(([key, priority]) => (
                                    <option key={key} value={key}>{priority.label}</option>
                                ))}
                            </select>
                        </div>

                        <div className="flex justify-end space-x-3 pt-4">
                            <button
                                type="button"
                                onClick={onCancel}
                                className="btn-secondary"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="btn-primary"
                            >
                                Save Log
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default MachineDetailsScreen;
