import React from 'react';
import { HashRouter, Routes, Route } from 'react-router-dom';
import HomeScreen from './pages/HomeScreen';
import MachineDetailsScreen from './pages/MachineDetailsScreen';
import MaintenanceLogScreen from './pages/MaintenanceLogScreen';
import SettingsScreen from './pages/SettingsScreen';
import Header from './components/Header';

function App() {
  return (
    <HashRouter>
      <div className="min-h-screen bg-gray-50 text-gray-800">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto">
          <Routes>
            <Route path="/" element={<HomeScreen />} />
            <Route path="/machine/:id" element={<MachineDetailsScreen />} />
            <Route path="/machine/:id/log" element={<MaintenanceLogScreen />} />
            <Route path="/settings" element={<SettingsScreen />} />
          </Routes>
        </main>
      </div>
    </HashRouter>
  );
}

export default App;