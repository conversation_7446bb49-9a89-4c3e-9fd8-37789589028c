// DialysisCare - Theme Provider Component
// Manages application theming and provides theme context

import React, { createContext, useContext, useState, useEffect } from 'react';

// Available themes
export const THEMES = {
    light: {
        name: 'Light',
        description: 'Clean, professional light theme',
        icon: 'sun'
    },
    dark: {
        name: 'Dark',
        description: 'Reduced eye strain dark theme',
        icon: 'moon'
    },
    'high-contrast': {
        name: 'High Contrast',
        description: 'Enhanced accessibility theme',
        icon: 'contrast'
    },
    medical: {
        name: 'Medical',
        description: 'Specialized medical environment theme',
        icon: 'medical'
    }
};

// Create theme context
const ThemeContext = createContext();

// Theme provider component
export const ThemeProvider = ({ children }) => {
    const [currentTheme, setCurrentTheme] = useState('light');
    const [systemPreference, setSystemPreference] = useState('light');

    // Initialize theme on mount
    useEffect(() => {
        // Check system preference
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        setSystemPreference(mediaQuery.matches ? 'dark' : 'light');

        // Load saved theme or use system preference
        const savedTheme = localStorage.getItem('dialysiscare-theme');
        if (savedTheme && THEMES[savedTheme]) {
            setCurrentTheme(savedTheme);
            applyTheme(savedTheme);
        } else {
            const preferredTheme = mediaQuery.matches ? 'dark' : 'light';
            setCurrentTheme(preferredTheme);
            applyTheme(preferredTheme);
        }

        // Listen for system theme changes
        const handleSystemThemeChange = (e) => {
            setSystemPreference(e.matches ? 'dark' : 'light');
            // Only auto-switch if no theme is explicitly saved
            if (!localStorage.getItem('dialysiscare-theme')) {
                const newTheme = e.matches ? 'dark' : 'light';
                setCurrentTheme(newTheme);
                applyTheme(newTheme);
            }
        };

        mediaQuery.addEventListener('change', handleSystemThemeChange);
        return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
    }, []);

    // Apply theme to document
    const applyTheme = (theme) => {
        document.documentElement.setAttribute('data-theme', theme);
        
        // Update meta theme-color for mobile browsers
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            const themeColors = {
                light: '#ffffff',
                dark: '#1f2937',
                'high-contrast': '#000000',
                medical: '#1e40af'
            };
            metaThemeColor.setAttribute('content', themeColors[theme] || themeColors.light);
        }
    };

    // Change theme
    const changeTheme = (theme) => {
        if (!THEMES[theme]) {
            console.warn(`Theme "${theme}" is not available`);
            return;
        }

        setCurrentTheme(theme);
        applyTheme(theme);
        localStorage.setItem('dialysiscare-theme', theme);

        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('theme-changed', {
            detail: { theme, previousTheme: currentTheme }
        }));
    };

    // Toggle between light and dark themes
    const toggleTheme = () => {
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        changeTheme(newTheme);
    };

    // Reset to system preference
    const resetToSystemTheme = () => {
        localStorage.removeItem('dialysiscare-theme');
        setCurrentTheme(systemPreference);
        applyTheme(systemPreference);
    };

    // Get theme icon
    const getThemeIcon = (theme) => {
        const iconClass = "w-5 h-5";
        
        switch (THEMES[theme]?.icon) {
            case 'sun':
                return (
                    <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                );
            case 'moon':
                return (
                    <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                );
            case 'contrast':
                return (
                    <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2V3z" />
                    </svg>
                );
            case 'medical':
                return (
                    <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                );
            default:
                return (
                    <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4z" />
                    </svg>
                );
        }
    };

    const value = {
        currentTheme,
        systemPreference,
        availableThemes: THEMES,
        changeTheme,
        toggleTheme,
        resetToSystemTheme,
        getThemeIcon
    };

    return (
        <ThemeContext.Provider value={value}>
            {children}
        </ThemeContext.Provider>
    );
};

// Custom hook to use theme context
export const useTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

// Theme selector component
export const ThemeSelector = ({ className = '' }) => {
    const { currentTheme, availableThemes, changeTheme, getThemeIcon } = useTheme();

    return (
        <div className={`theme-selector ${className}`}>
            <label className="block text-sm font-medium text-gray-700 mb-2">
                Theme
            </label>
            <div className="grid grid-cols-2 gap-2">
                {Object.entries(availableThemes).map(([themeKey, theme]) => (
                    <button
                        key={themeKey}
                        onClick={() => changeTheme(themeKey)}
                        className={`
                            flex items-center space-x-2 p-3 rounded-lg border transition-all
                            ${currentTheme === themeKey
                                ? 'border-blue-500 bg-blue-50 text-blue-700'
                                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                            }
                        `}
                    >
                        {getThemeIcon(themeKey)}
                        <div className="text-left">
                            <div className="font-medium text-sm">{theme.name}</div>
                            <div className="text-xs opacity-75">{theme.description}</div>
                        </div>
                    </button>
                ))}
            </div>
        </div>
    );
};

export default ThemeProvider;
