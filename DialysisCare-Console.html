<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DialysisCare - Machine Operating Console</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .fade-in { animation: fadeIn 0.5s ease-in-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .parameter-card {
            transition: all 0.3s ease;
        }
        .parameter-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .status-normal { @apply bg-green-50 border-green-200 text-green-800; }
        .status-warning { @apply bg-yellow-50 border-yellow-200 text-yellow-800; }
        .status-alarm { @apply bg-red-50 border-red-200 text-red-800; }
        .pulse-alarm { animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .progress-bar {
            transition: width 0.5s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <!-- Header -->
    <header class="bg-blue-600 text-white py-4 px-6 shadow-lg sticky top-0 z-30">
        <div class="flex justify-between items-center">
            <h1 class="text-2xl font-bold flex items-center">
                <i class="fas fa-heart-pulse mr-3"></i>
                DialysisCare - Operating Console
            </h1>
            <div class="flex items-center space-x-4">
                <div id="connectionStatus" class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    🟢 Online
                </div>
                <div id="treatmentStatus" class="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                    ⏸️ Standby
                </div>
                <div id="currentTime" class="text-sm font-mono">
                    --:--:--
                </div>
            </div>
        </div>
    </header>

    <!-- Main Console -->
    <main class="p-6 space-y-6">
        
        <!-- Machine Selection & Control Panel -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Machine Control Panel</h2>
                    <p class="text-gray-600">Real-time monitoring and control interface</p>
                </div>
                <div class="flex items-center space-x-3">
                    <select id="machineSelect" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="HD-001">HD-001 - Fresenius 5008S (Room A1)</option>
                        <option value="HD-002">HD-002 - Fresenius 5008S (Room A2)</option>
                        <option value="HD-003">HD-003 - Fresenius 5008S (Room B1)</option>
                        <option value="HD-004">HD-004 - Fresenius 5008S (Room B2)</option>
                        <option value="HD-005">HD-005 - Fresenius 5008S (Room C1)</option>
                    </select>
                </div>
            </div>

            <!-- Treatment Control -->
            <div class="flex items-center space-x-4 mb-6">
                <button id="startTreatmentBtn" class="px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors">
                    ▶️ Start Treatment
                </button>
                <button id="stopTreatmentBtn" class="px-6 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>
                    ⏹️ Stop Treatment
                </button>
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-600">Treatment Time:</span>
                    <span id="treatmentTimer" class="text-lg font-mono font-bold text-blue-600">00:00</span>
                </div>
                <div id="alarmAcknowledge" class="hidden">
                    <button id="ackAlarmBtn" class="px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 pulse-alarm">
                        🚨 ACKNOWLEDGE ALARM
                    </button>
                </div>
            </div>
        </div>

        <!-- Real-time Parameters Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            
            <!-- Blood Circuit -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4 text-red-700 flex items-center">
                    🩸 Blood Circuit
                </h3>
                <div class="space-y-4">
                    <div id="bloodFlow" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Blood Flow Rate</span>
                            <span class="text-xl font-mono">300 mL/min</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: 200-450 mL/min</div>
                    </div>
                    
                    <div id="arterialPressure" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Arterial Pressure</span>
                            <span class="text-xl font-mono">-180 mmHg</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: -100 to -250 mmHg</div>
                    </div>
                    
                    <div id="venousPressure" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Venous Pressure</span>
                            <span class="text-xl font-mono">120 mmHg</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: 50-200 mmHg</div>
                    </div>
                </div>
            </div>

            <!-- Dialysate Circuit -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4 text-blue-700 flex items-center">
                    💧 Dialysate Circuit
                </h3>
                <div class="space-y-4">
                    <div id="dialysateFlow" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Dialysate Flow</span>
                            <span class="text-xl font-mono">500 mL/min</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: 300-800 mL/min</div>
                    </div>
                    
                    <div id="dialysateTemp" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Temperature</span>
                            <span class="text-xl font-mono">36.5 °C</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: 35.5-37.5 °C</div>
                    </div>
                    
                    <div id="conductivity" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Conductivity</span>
                            <span class="text-xl font-mono">14.0 mS/cm</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: 13.0-15.0 mS/cm</div>
                    </div>
                </div>
            </div>

            <!-- Ultrafiltration -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4 text-purple-700 flex items-center">
                    ⚖️ Ultrafiltration
                </h3>
                <div class="space-y-4">
                    <div id="ufRate" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">UF Rate</span>
                            <span class="text-xl font-mono">0.8 L/hr</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: 0.0-2.0 L/hr</div>
                    </div>
                    
                    <div id="ufRemoved" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">UF Removed</span>
                            <span class="text-xl font-mono">0.60 L</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Goal: 2.5 L</div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div id="ufProgress" class="bg-purple-600 h-2 rounded-full progress-bar" style="width: 24%"></div>
                        </div>
                    </div>
                    
                    <div id="tmp" class="parameter-card p-3 rounded-lg border status-normal">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">TMP</span>
                            <span class="text-xl font-mono">150 mmHg</span>
                        </div>
                        <div class="text-xs mt-1 text-gray-500">Range: 0-300 mmHg</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quality Standards & Water Quality -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <!-- Water Quality -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4 text-cyan-700 flex items-center">
                    🚰 Water Quality Standards
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-2 bg-green-50 border border-green-200 rounded">
                        <span class="text-sm font-medium">Bacteria Count</span>
                        <span class="text-sm font-mono">5 / 100 CFU/mL</span>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-green-50 border border-green-200 rounded">
                        <span class="text-sm font-medium">Endotoxin Level</span>
                        <span class="text-sm font-mono">0.03 / 0.25 EU/mL</span>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-green-50 border border-green-200 rounded">
                        <span class="text-sm font-medium">Chlorine</span>
                        <span class="text-sm font-mono">0.02 / 0.1 mg/L</span>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-green-50 border border-green-200 rounded">
                        <span class="text-sm font-medium">Water Pressure</span>
                        <span class="text-sm font-mono">2.8 bar</span>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4 text-green-700 flex items-center">
                    📊 Performance Metrics
                </h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">1,456</div>
                        <div class="text-xs text-gray-500">Total Treatments</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">Excellent</div>
                        <div class="text-xs text-gray-500">Water Quality</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600" id="ktvRatio">1.4</div>
                        <div class="text-xs text-gray-500">Kt/V Ratio</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">2,847</div>
                        <div class="text-xs text-gray-500">Operating Hours</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Panel -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold mb-4">Console Actions</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="px-4 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    📊 View Trends
                </button>
                <button class="px-4 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors">
                    🧪 Run Quality Test
                </button>
                <button class="px-4 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors">
                    📋 Export Report
                </button>
                <button class="px-4 py-3 bg-orange-600 text-white rounded-lg font-medium hover:bg-orange-700 transition-colors">
                    ⚙️ Calibrate
                </button>
            </div>
        </div>

    </main>

    <script>
        // Application State
        let isOnline = navigator.onLine;
        let treatmentActive = false;
        let treatmentStartTime = null;
        let alarmActive = false;
        let currentMachine = 'HD-001';
        let treatmentSeconds = 0;

        // Parameter ranges and current values
        const parameters = {
            bloodFlow: { value: 300, min: 200, max: 450, unit: 'mL/min' },
            arterialPressure: { value: -180, min: -250, max: -100, unit: 'mmHg' },
            venousPressure: { value: 120, min: 50, max: 200, unit: 'mmHg' },
            dialysateFlow: { value: 500, min: 300, max: 800, unit: 'mL/min' },
            dialysateTemp: { value: 36.5, min: 35.5, max: 37.5, unit: '°C' },
            conductivity: { value: 14.0, min: 13.0, max: 15.0, unit: 'mS/cm' },
            ufRate: { value: 0.8, min: 0.0, max: 2.0, unit: 'L/hr' },
            ufRemoved: { value: 0.60, min: 0.0, max: 5.0, unit: 'L', goal: 2.5 },
            tmp: { value: 150, min: 0, max: 300, unit: 'mmHg' },
            ktvRatio: { value: 1.4, min: 1.2, max: 2.0, unit: '' }
        };

        // DOM Elements
        const elements = {
            connectionStatus: document.getElementById('connectionStatus'),
            treatmentStatus: document.getElementById('treatmentStatus'),
            currentTime: document.getElementById('currentTime'),
            machineSelect: document.getElementById('machineSelect'),
            startBtn: document.getElementById('startTreatmentBtn'),
            stopBtn: document.getElementById('stopTreatmentBtn'),
            treatmentTimer: document.getElementById('treatmentTimer'),
            alarmAcknowledge: document.getElementById('alarmAcknowledge'),
            ackAlarmBtn: document.getElementById('ackAlarmBtn')
        };

        // Utility Functions
        function updateTime() {
            const now = new Date();
            elements.currentTime.textContent = now.toLocaleTimeString();
        }

        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        function getParameterStatus(param) {
            if (param.value < param.min || param.value > param.max) {
                return 'alarm';
            } else if (param.value < param.min * 1.1 || param.value > param.max * 0.9) {
                return 'warning';
            }
            return 'normal';
        }

        function updateParameterDisplay(paramId, param) {
            const element = document.getElementById(paramId);
            if (!element) return;

            const status = getParameterStatus(param);
            const valueSpan = element.querySelector('.text-xl');

            // Update value
            valueSpan.textContent = `${param.value.toFixed(param.unit === '°C' || param.unit === 'L' || param.unit === 'L/hr' ? 1 : 0)} ${param.unit}`;

            // Update status styling
            element.className = element.className.replace(/status-\w+/, `status-${status}`);

            if (status === 'alarm') {
                alarmActive = true;
                elements.alarmAcknowledge.classList.remove('hidden');
            }
        }

        // Real-time parameter simulation
        function simulateParameters() {
            if (!treatmentActive) return;

            // Simulate realistic parameter fluctuations
            parameters.bloodFlow.value += (Math.random() - 0.5) * 10;
            parameters.arterialPressure.value += (Math.random() - 0.5) * 20;
            parameters.venousPressure.value += (Math.random() - 0.5) * 15;
            parameters.dialysateFlow.value += (Math.random() - 0.5) * 20;
            parameters.dialysateTemp.value += (Math.random() - 0.5) * 0.2;
            parameters.conductivity.value += (Math.random() - 0.5) * 0.3;
            parameters.ufRate.value += (Math.random() - 0.5) * 0.1;
            parameters.tmp.value += (Math.random() - 0.5) * 20;

            // Gradual UF removal
            parameters.ufRemoved.value += 0.005; // 5ml per second

            // Update Kt/V ratio based on treatment progress
            const treatmentProgress = treatmentSeconds / (4 * 3600); // 4 hour treatment
            parameters.ktvRatio.value = 1.2 + (treatmentProgress * 0.8);

            // Constrain values to realistic ranges
            Object.keys(parameters).forEach(key => {
                const param = parameters[key];
                param.value = Math.max(param.min, Math.min(param.max, param.value));
            });

            // Update displays
            updateParameterDisplay('bloodFlow', parameters.bloodFlow);
            updateParameterDisplay('arterialPressure', parameters.arterialPressure);
            updateParameterDisplay('venousPressure', parameters.venousPressure);
            updateParameterDisplay('dialysateFlow', parameters.dialysateFlow);
            updateParameterDisplay('dialysateTemp', parameters.dialysateTemp);
            updateParameterDisplay('conductivity', parameters.conductivity);
            updateParameterDisplay('ufRate', parameters.ufRate);
            updateParameterDisplay('ufRemoved', parameters.ufRemoved);
            updateParameterDisplay('tmp', parameters.tmp);

            // Update UF progress bar
            const ufProgress = (parameters.ufRemoved.value / parameters.ufRemoved.goal) * 100;
            document.getElementById('ufProgress').style.width = `${Math.min(100, ufProgress)}%`;

            // Update Kt/V display
            document.getElementById('ktvRatio').textContent = parameters.ktvRatio.value.toFixed(2);
        }

        // Treatment control functions
        function startTreatment() {
            treatmentActive = true;
            treatmentStartTime = new Date();
            treatmentSeconds = 0;

            elements.startBtn.disabled = true;
            elements.stopBtn.disabled = false;
            elements.treatmentStatus.innerHTML = '▶️ Treatment Active';
            elements.treatmentStatus.className = 'px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800';

            console.log('Treatment started for machine:', currentMachine);
        }

        function stopTreatment() {
            treatmentActive = false;
            treatmentStartTime = null;

            elements.startBtn.disabled = false;
            elements.stopBtn.disabled = true;
            elements.treatmentStatus.innerHTML = '⏸️ Standby';
            elements.treatmentStatus.className = 'px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800';

            console.log('Treatment stopped for machine:', currentMachine);
        }

        function acknowledgeAlarm() {
            alarmActive = false;
            elements.alarmAcknowledge.classList.add('hidden');

            // Reset all parameter statuses to normal (in real system, would address actual issues)
            document.querySelectorAll('.parameter-card').forEach(card => {
                card.className = card.className.replace(/status-\w+/, 'status-normal');
            });

            console.log('Alarm acknowledged for machine:', currentMachine);
        }

        // Online/Offline detection
        function updateConnectionStatus() {
            isOnline = navigator.onLine;
            elements.connectionStatus.innerHTML = isOnline ? '🟢 Online' : '🔴 Offline';
            elements.connectionStatus.className = isOnline
                ? 'px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800'
                : 'px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800';
        }

        // Event Listeners
        elements.startBtn.addEventListener('click', startTreatment);
        elements.stopBtn.addEventListener('click', stopTreatment);
        elements.ackAlarmBtn.addEventListener('click', acknowledgeAlarm);

        elements.machineSelect.addEventListener('change', (e) => {
            currentMachine = e.target.value;
            console.log('Selected machine:', currentMachine);

            // Reset treatment state when switching machines
            if (treatmentActive) {
                stopTreatment();
            }
        });

        // Online/Offline event listeners
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Main update loop
        setInterval(() => {
            updateTime();

            if (treatmentActive) {
                treatmentSeconds++;
                elements.treatmentTimer.textContent = formatTime(treatmentSeconds);
                simulateParameters();
            }
        }, 1000);

        // Initialize
        updateTime();
        updateConnectionStatus();

        // Initial parameter display
        Object.keys(parameters).forEach(key => {
            if (document.getElementById(key)) {
                updateParameterDisplay(key, parameters[key]);
            }
        });

        console.log('DialysisCare Operating Console initialized');
        console.log('Selected machine:', currentMachine);
        console.log('System ready for operation');

    </script>
</body>
</html>
