// DialysisCare - Machine List Screen with Offline Support
// List of all machines with offline awareness

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const MachineListScreen = () => {
    console.log('MachineListScreen rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    // Sample machine data (would normally come from API/database)
    const machines = [
        {
            id: 'HD-001',
            name: 'Hemodialysis Unit 1',
            status: 'operational',
            lastMaintenance: '2024-06-15',
            nextMaintenance: '2024-07-15',
            location: 'Room A1'
        },
        {
            id: 'HD-002',
            name: 'Hemodialysis Unit 2',
            status: 'maintenance',
            lastMaintenance: '2024-06-10',
            nextMaintenance: '2024-07-04',
            location: 'Room A2'
        },
        {
            id: 'HD-003',
            name: 'Hemodialysis Unit 3',
            status: 'operational',
            lastMaintenance: '2024-06-20',
            nextMaintenance: '2024-07-20',
            location: 'Room B1'
        },
        {
            id: 'HD-004',
            name: 'Hemodialysis Unit 4',
            status: 'operational',
            lastMaintenance: '2024-06-18',
            nextMaintenance: '2024-07-18',
            location: 'Room B2'
        },
        {
            id: 'HD-005',
            name: 'Hemodialysis Unit 5',
            status: 'offline',
            lastMaintenance: '2024-06-05',
            nextMaintenance: '2024-07-05',
            location: 'Room C1'
        }
    ];

    const getStatusColor = (status) => {
        switch (status) {
            case 'operational':
                return 'bg-green-100 text-green-800';
            case 'maintenance':
                return 'bg-yellow-100 text-yellow-800';
            case 'offline':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'operational':
                return (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                );
            case 'maintenance':
                return (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                    </svg>
                );
            case 'offline':
                return (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
                    </svg>
                );
            default:
                return null;
        }
    };
    
    return (
        <div className="p-6">
            <div className="flex items-center justify-between mb-6">
                <h1 className="text-3xl font-bold text-gray-900">
                    Hemodialysis Machines
                </h1>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                    {isOnline ? 'Live Data' : 'Cached Data'}
                </div>
            </div>

            {!isOnline && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                        <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                        </svg>
                        <div>
                            <h3 className="text-sm font-medium text-yellow-800">Offline Mode</h3>
                            <p className="text-sm text-yellow-700">Showing cached machine data. Real-time updates unavailable.</p>
                        </div>
                    </div>
                </div>
            )}

            <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <ul className="divide-y divide-gray-200">
                    {machines.map((machine) => (
                        <li key={machine.id}>
                            <Link 
                                to={`/machine/${machine.id}`}
                                className="block hover:bg-gray-50 transition-colors duration-150"
                            >
                                <div className="px-4 py-4 sm:px-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0">
                                                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div className="ml-4">
                                                <div className="flex items-center">
                                                    <p className="text-sm font-medium text-gray-900">
                                                        {machine.name}
                                                    </p>
                                                    <span className="ml-2 text-sm text-gray-500">
                                                        ({machine.id})
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-500">
                                                    {machine.location} • Last maintenance: {machine.lastMaintenance}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center">
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(machine.status)}`}>
                                                {getStatusIcon(machine.status)}
                                                <span className="ml-1 capitalize">{machine.status}</span>
                                            </span>
                                            <svg className="ml-2 w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    
                                    {machine.status === 'maintenance' && (
                                        <div className="mt-2 text-sm text-yellow-700">
                                            ⚠️ Maintenance due: {machine.nextMaintenance}
                                        </div>
                                    )}
                                </div>
                            </Link>
                        </li>
                    ))}
                </ul>
            </div>

            <div className="mt-6 flex justify-between items-center">
                <p className="text-sm text-gray-500">
                    Showing {machines.length} machines
                    {!isOnline && ' (cached data)'}
                </p>
                
                <button 
                    className={`px-4 py-2 rounded font-medium transition-colors ${
                        isOnline 
                            ? 'bg-blue-600 text-white hover:bg-blue-700' 
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                    disabled={!isOnline}
                    title={!isOnline ? 'This feature requires an internet connection' : ''}
                >
                    Add New Machine
                </button>
            </div>
        </div>
    );
};

export default MachineListScreen;
