// DialysisCare - Offline Indicator Component
// Shows connection status and offline capabilities

import React, { useState, useEffect } from 'react';

const OfflineIndicator = () => {
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [showIndicator, setShowIndicator] = useState(false);
    const [offlineQueue, setOfflineQueue] = useState([]);

    useEffect(() => {
        // Handle online/offline events
        const handleOnline = () => {
            setIsOnline(true);
            setShowIndicator(true);
            
            // Show "back online" message briefly
            setTimeout(() => {
                setShowIndicator(false);
            }, 3000);

            // Process offline queue
            processOfflineQueue();
        };

        const handleOffline = () => {
            setIsOnline(false);
            setShowIndicator(true);
        };

        // Listen for connection changes
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        // Listen for custom offline queue events
        const handleOfflineAction = (event) => {
            if (!isOnline) {
                setOfflineQueue(prev => [...prev, event.detail]);
            }
        };

        window.addEventListener('offline-action', handleOfflineAction);

        // Initial state
        if (!isOnline) {
            setShowIndicator(true);
        }

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
            window.removeEventListener('offline-action', handleOfflineAction);
        };
    }, [isOnline]);

    const processOfflineQueue = async () => {
        if (offlineQueue.length === 0) return;

        console.log(`Processing ${offlineQueue.length} offline actions...`);
        
        // Process each queued action
        for (const action of offlineQueue) {
            try {
                // Dispatch the action to be processed
                window.dispatchEvent(new CustomEvent('process-offline-action', {
                    detail: action
                }));
            } catch (error) {
                console.error('Failed to process offline action:', error);
            }
        }

        // Clear the queue
        setOfflineQueue([]);
    };

    const handleRetry = () => {
        // Force a connection check
        if (navigator.onLine) {
            setIsOnline(true);
            processOfflineQueue();
        }
    };

    const handleDismiss = () => {
        setShowIndicator(false);
    };

    if (!showIndicator) return null;

    return (
        <div className={`
            fixed bottom-4 left-4 right-4 sm:left-4 sm:right-auto sm:max-w-sm
            z-50 transform transition-all duration-300 ease-in-out
            ${showIndicator ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}
        `}>
            <div className={`
                rounded-lg shadow-lg border-l-4 p-4 flex items-start space-x-3
                ${isOnline 
                    ? 'bg-green-50 border-green-400 text-green-800' 
                    : 'bg-orange-50 border-orange-400 text-orange-800'
                }
            `}>
                <div className="flex-shrink-0">
                    {isOnline ? (
                        <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                        </svg>
                    ) : (
                        <svg className="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-12.728 12.728m0 0L5.636 18.364m12.728-12.728L18.364 5.636m-12.728 12.728L5.636 18.364" />
                        </svg>
                    )}
                </div>

                <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm">
                        {isOnline ? 'Back Online' : 'You\'re Offline'}
                    </h4>
                    <p className="text-sm mt-1">
                        {isOnline ? (
                            offlineQueue.length > 0 
                                ? `Syncing ${offlineQueue.length} pending changes...`
                                : 'All features are now available.'
                        ) : (
                            'Some features may be limited. Changes will sync when connection is restored.'
                        )}
                    </p>

                    {!isOnline && offlineQueue.length > 0 && (
                        <div className="mt-2 text-xs bg-orange-100 rounded px-2 py-1">
                            {offlineQueue.length} action{offlineQueue.length !== 1 ? 's' : ''} queued for sync
                        </div>
                    )}
                </div>

                <div className="flex-shrink-0 flex space-x-1">
                    {!isOnline && (
                        <button
                            onClick={handleRetry}
                            className="text-orange-600 hover:text-orange-800 transition-colors"
                            title="Retry connection"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </button>
                    )}
                    
                    <button
                        onClick={handleDismiss}
                        className={`transition-colors ${
                            isOnline 
                                ? 'text-green-600 hover:text-green-800' 
                                : 'text-orange-600 hover:text-orange-800'
                        }`}
                        title="Dismiss"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );
};

export default OfflineIndicator;
