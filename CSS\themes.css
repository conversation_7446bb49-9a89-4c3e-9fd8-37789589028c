/* DialysisCare - Theme Styles */
/* Professional Medical Theme System */

/* Light Theme (Default) */
:root {
    --theme-background: #ffffff;
    --theme-surface: #f9fafb;
    --theme-primary: var(--medical-blue-600);
    --theme-primary-hover: var(--medical-blue-700);
    --theme-text-primary: var(--gray-900);
    --theme-text-secondary: var(--gray-600);
    --theme-border: var(--gray-200);
    --theme-shadow: rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
[data-theme="dark"] {
    --theme-background: #1f2937;
    --theme-surface: #374151;
    --theme-primary: var(--medical-blue-500);
    --theme-primary-hover: var(--medical-blue-400);
    --theme-text-primary: #f9fafb;
    --theme-text-secondary: #d1d5db;
    --theme-border: #4b5563;
    --theme-shadow: rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] body {
    background-color: var(--theme-background);
    color: var(--theme-text-primary);
}

[data-theme="dark"] .card {
    background-color: var(--theme-surface);
    border-color: var(--theme-border);
    color: var(--theme-text-primary);
}

[data-theme="dark"] .machine-card-header {
    background: linear-gradient(135deg, var(--gray-700) 0%, var(--gray-600) 100%);
}

[data-theme="dark"] .form-input,
[data-theme="dark"] .form-select,
[data-theme="dark"] .form-textarea {
    background-color: var(--theme-surface);
    border-color: var(--theme-border);
    color: var(--theme-text-primary);
}

/* High Contrast Theme */
[data-theme="high-contrast"] {
    --theme-background: #000000;
    --theme-surface: #1a1a1a;
    --theme-primary: #ffffff;
    --theme-primary-hover: #e5e5e5;
    --theme-text-primary: #ffffff;
    --theme-text-secondary: #cccccc;
    --theme-border: #666666;
    --theme-shadow: rgba(255, 255, 255, 0.2);
}

[data-theme="high-contrast"] body {
    background-color: var(--theme-background);
    color: var(--theme-text-primary);
}

[data-theme="high-contrast"] .card {
    background-color: var(--theme-surface);
    border: 2px solid var(--theme-border);
}

[data-theme="high-contrast"] .btn-primary {
    background-color: var(--theme-text-primary);
    color: var(--theme-background);
    border: 2px solid var(--theme-text-primary);
}

[data-theme="high-contrast"] .btn-primary:hover {
    background-color: var(--theme-background);
    color: var(--theme-text-primary);
}

/* Medical Professional Theme */
[data-theme="medical"] {
    --theme-background: #fafcff;
    --theme-surface: #ffffff;
    --theme-primary: #0066cc;
    --theme-primary-hover: #0052a3;
    --theme-text-primary: #1a365d;
    --theme-text-secondary: #4a5568;
    --theme-border: #e2e8f0;
    --theme-shadow: rgba(0, 102, 204, 0.1);
}

[data-theme="medical"] .header {
    background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
}

[data-theme="medical"] .machine-card {
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px rgba(0, 102, 204, 0.1);
}

[data-theme="medical"] .status-indicator.status-operational {
    color: #047857;
}

[data-theme="medical"] .status-indicator.status-operational .status-indicator-dot {
    background-color: #10b981;
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
        --theme-background: #1f2937;
        --theme-surface: #374151;
        --theme-primary: var(--medical-blue-500);
        --theme-primary-hover: var(--medical-blue-400);
        --theme-text-primary: #f9fafb;
        --theme-text-secondary: #d1d5db;
        --theme-border: #4b5563;
        --theme-shadow: rgba(0, 0, 0, 0.3);
    }
}

/* Theme Toggle Button */
.theme-toggle {
    position: relative;
    width: 3rem;
    height: 1.5rem;
    background-color: var(--gray-300);
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.theme-toggle:focus {
    outline: 2px solid var(--medical-blue-500);
    outline-offset: 2px;
}

.theme-toggle.active {
    background-color: var(--medical-blue-600);
}

.theme-toggle::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 50%;
    transition: transform var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.theme-toggle.active::after {
    transform: translateX(1.5rem);
}

/* Print Theme */
@media print {
    :root {
        --theme-background: white;
        --theme-surface: white;
        --theme-primary: black;
        --theme-text-primary: black;
        --theme-text-secondary: #666666;
        --theme-border: #cccccc;
        --theme-shadow: none;
    }
    
    .header {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        border-bottom: 2px solid black;
    }
    
    .machine-card,
    .log-entry-card {
        box-shadow: none !important;
        border: 1px solid #cccccc !important;
        break-inside: avoid;
    }
    
    .fab,
    .theme-toggle,
    .no-print {
        display: none !important;
    }
}

/* Color Blind Friendly Adjustments */
.colorblind-friendly {
    --medical-green-500: #0066cc;
    --medical-red-500: #ff6600;
    --medical-orange-500: #9900cc;
}

.colorblind-friendly .status-indicator.status-operational .status-indicator-dot {
    background-color: #0066cc;
    border: 2px solid #004499;
}

.colorblind-friendly .status-indicator.status-maintenance .status-indicator-dot {
    background-color: #9900cc;
    border: 2px solid #660066;
}

.colorblind-friendly .status-indicator.status-offline .status-indicator-dot {
    background-color: #ff6600;
    border: 2px solid #cc3300;
}

/* Large Text Theme */
.large-text {
    font-size: 1.125rem;
}

.large-text h1 {
    font-size: 2.5rem;
}

.large-text h2 {
    font-size: 2rem;
}

.large-text h3 {
    font-size: 1.75rem;
}

.large-text .btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
}

.large-text .form-input,
.large-text .form-select,
.large-text .form-textarea {
    padding: var(--spacing-md);
    font-size: 1rem;
}

/* Focus Indicators for Accessibility */
.focus-visible:focus {
    outline: 3px solid var(--medical-blue-500);
    outline-offset: 2px;
}

.focus-visible:focus:not(:focus-visible) {
    outline: none;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Status-specific theme adjustments */
.status-critical {
    --status-color: var(--medical-red-600);
    --status-bg: var(--medical-red-50);
    --status-border: var(--medical-red-200);
}

.status-warning {
    --status-color: var(--medical-orange-600);
    --status-bg: var(--medical-orange-50);
    --status-border: var(--medical-orange-200);
}

.status-good {
    --status-color: var(--medical-green-600);
    --status-bg: var(--medical-green-50);
    --status-border: var(--medical-green-200);
}

.status-unknown {
    --status-color: var(--gray-600);
    --status-bg: var(--gray-50);
    --status-border: var(--gray-200);
}
