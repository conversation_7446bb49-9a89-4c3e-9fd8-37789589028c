import { Machine } from './types';

export const MACHINES: Machine[] = [
  {
    id: '1',
    model: 'Fresenius 5008',
    serialNumber: '1234567890',
    location: 'Room 1',
    lastMaintenanceDate: new Date(new Date().setDate(new Date().getDate() - 30)),
    notes: 'Primary machine for Ward A. Filter A was replaced in the last cycle. Check pressure sensors.',
    maintenanceLogs: [
      {
        id: 'log1-1',
        date: new Date(new Date().setDate(new Date().getDate() - 30)),
        description: 'Replaced primary dialyzer filter and ran calibration sequence.',
        technician: '<PERSON>',
      },
      {
        id: 'log1-2',
        date: new Date(new Date().setDate(new Date().getDate() - 75)),
        description: 'Calibrated fluid flow meter and checked for leaks.',
        technician: '<PERSON>',
      },
       {
        id: 'log1-3',
        date: new Date(new Date().setDate(new Date().getDate() - 120)),
        description: 'Quarterly deep cleaning and system flush.',
        technician: '<PERSON>',
      },
    ],
  },
  {
    id: '2',
    model: 'Gambro AK 96',
    serialNumber: '0987654321',
    location: 'Room 2',
    lastMaintenanceDate: new Date(new Date().setDate(new Date().getDate() - 15)),
    notes: 'Backup machine. Low usage hours.',
    maintenanceLogs: [
       {
        id: 'log2-1',
        date: new Date(new Date().setDate(new Date().getDate() - 15)),
        description: 'Routine software update and diagnostics check.',
        technician: 'Alex Ray',
      },
      {
        id: 'log2-2',
        date: new Date(new Date().setDate(new Date().getDate() - 105)),
        description: 'Replaced battery backup unit.',
        technician: 'Jane Smith',
      },
    ],
  },
  {
    id: '3',
    model: 'Baxter Artis',
    serialNumber: '1122334455',
    location: 'Room 3',
    lastMaintenanceDate: undefined,
    notes: 'Newly commissioned machine. Awaiting first scheduled maintenance.',
    maintenanceLogs: [],
  },
   {
    id: '4',
    model: 'Nipro Surdial X',
    serialNumber: '5566778899',
    location: 'Mobile Unit A',
    lastMaintenanceDate: new Date(new Date().setDate(new Date().getDate() - 5)),
    notes: 'Used in the mobile dialysis van. Subject to more frequent vibration checks.',
    maintenanceLogs: [
       {
        id: 'log4-1',
        date: new Date(new Date().setDate(new Date().getDate() - 5)),
        description: 'Vibration dampener check and sensor recalibration.',
        technician: 'Maria Garcia',
      },
      {
        id: 'log4-2',
        date: new Date(new Date().setDate(new Date().getDate() - 35)),
        description: 'Full system diagnostics after long-distance travel.',
        technician: 'Maria Garcia',
      },
    ],
  },
];