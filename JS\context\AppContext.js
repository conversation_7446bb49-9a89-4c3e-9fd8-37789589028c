// DialysisCare - Application Context
// Provides global state management for the application

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { MACHINES } from '../constants.js';

// Initial state
const initialState = {
    machines: [],
    selectedMachine: null,
    loading: false,
    error: null,
    filters: {
        status: 'all',
        location: 'all',
        search: ''
    },
    sortBy: 'name',
    sortOrder: 'asc',
    viewMode: 'grid'
};

// Action types
const ActionTypes = {
    SET_LOADING: 'SET_LOADING',
    SET_ERROR: 'SET_ERROR',
    SET_MACHINES: 'SET_MACHINES',
    ADD_MACHINE: 'ADD_MACHINE',
    UPDATE_MACHINE: 'UPDATE_MACHINE',
    DELETE_MACHINE: 'DELETE_MACHINE',
    SELECT_MACHINE: 'SELECT_MACHINE',
    SET_FILTERS: 'SET_FILTERS',
    SET_SORT: 'SET_SORT',
    SET_VIEW_MODE: 'SET_VIEW_MODE',
    ADD_MAINTENANCE_LOG: 'ADD_MAINTENANCE_LOG',
    UPDATE_MAINTENANCE_LOG: 'UPDATE_MAINTENANCE_LOG',
    DELETE_MAINTENANCE_LOG: 'DELETE_MAINTENANCE_LOG'
};

// Reducer function
const appReducer = (state, action) => {
    switch (action.type) {
        case ActionTypes.SET_LOADING:
            return { ...state, loading: action.payload };
            
        case ActionTypes.SET_ERROR:
            return { ...state, error: action.payload, loading: false };
            
        case ActionTypes.SET_MACHINES:
            return { ...state, machines: action.payload, loading: false, error: null };
            
        case ActionTypes.ADD_MACHINE:
            const newMachines = [...state.machines, action.payload];
            saveMachinesToStorage(newMachines);
            return { ...state, machines: newMachines };
            
        case ActionTypes.UPDATE_MACHINE:
            const updatedMachines = state.machines.map(machine =>
                machine.id === action.payload.id ? { ...machine, ...action.payload } : machine
            );
            saveMachinesToStorage(updatedMachines);
            return { ...state, machines: updatedMachines };
            
        case ActionTypes.DELETE_MACHINE:
            const filteredMachines = state.machines.filter(machine => machine.id !== action.payload);
            saveMachinesToStorage(filteredMachines);
            return { 
                ...state, 
                machines: filteredMachines,
                selectedMachine: state.selectedMachine?.id === action.payload ? null : state.selectedMachine
            };
            
        case ActionTypes.SELECT_MACHINE:
            return { ...state, selectedMachine: action.payload };
            
        case ActionTypes.SET_FILTERS:
            return { ...state, filters: { ...state.filters, ...action.payload } };
            
        case ActionTypes.SET_SORT:
            return { ...state, sortBy: action.payload.sortBy, sortOrder: action.payload.sortOrder };
            
        case ActionTypes.SET_VIEW_MODE:
            return { ...state, viewMode: action.payload };
            
        case ActionTypes.ADD_MAINTENANCE_LOG:
            const machinesWithNewLog = state.machines.map(machine => {
                if (machine.id === action.payload.machineId) {
                    const newLog = {
                        ...action.payload.log,
                        id: Date.now().toString(),
                        date: new Date(action.payload.log.date)
                    };
                    return {
                        ...machine,
                        maintenanceLogs: [newLog, ...machine.maintenanceLogs],
                        lastMaintenanceDate: newLog.date
                    };
                }
                return machine;
            });
            saveMachinesToStorage(machinesWithNewLog);
            return { ...state, machines: machinesWithNewLog };
            
        case ActionTypes.UPDATE_MAINTENANCE_LOG:
            const machinesWithUpdatedLog = state.machines.map(machine => {
                if (machine.id === action.payload.machineId) {
                    return {
                        ...machine,
                        maintenanceLogs: machine.maintenanceLogs.map(log =>
                            log.id === action.payload.logId ? { ...log, ...action.payload.updates } : log
                        )
                    };
                }
                return machine;
            });
            saveMachinesToStorage(machinesWithUpdatedLog);
            return { ...state, machines: machinesWithUpdatedLog };
            
        case ActionTypes.DELETE_MAINTENANCE_LOG:
            const machinesWithDeletedLog = state.machines.map(machine => {
                if (machine.id === action.payload.machineId) {
                    return {
                        ...machine,
                        maintenanceLogs: machine.maintenanceLogs.filter(log => log.id !== action.payload.logId)
                    };
                }
                return machine;
            });
            saveMachinesToStorage(machinesWithDeletedLog);
            return { ...state, machines: machinesWithDeletedLog };
            
        default:
            return state;
    }
};

// Helper function to save machines to localStorage
const saveMachinesToStorage = (machines) => {
    try {
        localStorage.setItem('dialysiscare-machines', JSON.stringify(machines));
    } catch (error) {
        console.error('Failed to save machines to storage:', error);
    }
};

// Helper function to load machines from localStorage
const loadMachinesFromStorage = () => {
    try {
        const stored = localStorage.getItem('dialysiscare-machines');
        if (stored) {
            const machines = JSON.parse(stored);
            // Convert date strings back to Date objects
            return machines.map(machine => ({
                ...machine,
                lastMaintenanceDate: machine.lastMaintenanceDate ? new Date(machine.lastMaintenanceDate) : null,
                nextMaintenanceDate: machine.nextMaintenanceDate ? new Date(machine.nextMaintenanceDate) : null,
                maintenanceLogs: machine.maintenanceLogs.map(log => ({
                    ...log,
                    date: new Date(log.date)
                }))
            }));
        }
        return MACHINES; // Return default data if nothing in storage
    } catch (error) {
        console.error('Failed to load machines from storage:', error);
        return MACHINES;
    }
};

// Create context
const AppContext = createContext();

// Context provider component
export const AppProvider = ({ children }) => {
    const [state, dispatch] = useReducer(appReducer, initialState);

    // Load initial data
    useEffect(() => {
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        try {
            const machines = loadMachinesFromStorage();
            dispatch({ type: ActionTypes.SET_MACHINES, payload: machines });
        } catch (error) {
            dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        }
    }, []);

    // Action creators
    const actions = {
        setLoading: (loading) => dispatch({ type: ActionTypes.SET_LOADING, payload: loading }),
        setError: (error) => dispatch({ type: ActionTypes.SET_ERROR, payload: error }),
        setMachines: (machines) => dispatch({ type: ActionTypes.SET_MACHINES, payload: machines }),
        addMachine: (machine) => dispatch({ type: ActionTypes.ADD_MACHINE, payload: machine }),
        updateMachine: (machine) => dispatch({ type: ActionTypes.UPDATE_MACHINE, payload: machine }),
        deleteMachine: (machineId) => dispatch({ type: ActionTypes.DELETE_MACHINE, payload: machineId }),
        selectMachine: (machine) => dispatch({ type: ActionTypes.SELECT_MACHINE, payload: machine }),
        setFilters: (filters) => dispatch({ type: ActionTypes.SET_FILTERS, payload: filters }),
        setSort: (sortBy, sortOrder) => dispatch({ type: ActionTypes.SET_SORT, payload: { sortBy, sortOrder } }),
        setViewMode: (viewMode) => dispatch({ type: ActionTypes.SET_VIEW_MODE, payload: viewMode }),
        addMaintenanceLog: (machineId, log) => dispatch({ 
            type: ActionTypes.ADD_MAINTENANCE_LOG, 
            payload: { machineId, log } 
        }),
        updateMaintenanceLog: (machineId, logId, updates) => dispatch({ 
            type: ActionTypes.UPDATE_MAINTENANCE_LOG, 
            payload: { machineId, logId, updates } 
        }),
        deleteMaintenanceLog: (machineId, logId) => dispatch({ 
            type: ActionTypes.DELETE_MAINTENANCE_LOG, 
            payload: { machineId, logId } 
        })
    };

    const value = {
        ...state,
        ...actions
    };

    return (
        <AppContext.Provider value={value}>
            {children}
        </AppContext.Provider>
    );
};

// Custom hook to use the app context
export const useApp = () => {
    const context = useContext(AppContext);
    if (!context) {
        throw new Error('useApp must be used within an AppProvider');
    }
    return context;
};

export default AppContext;
