// DialysisCare - Simple Home Screen Component for Testing
// Minimal dashboard to test basic functionality

import React from 'react';

const HomeScreen = () => {
    console.log('HomeScreen component rendering...');
    
    return (
        <div className="p-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-6">
                DialysisCare Dashboard
            </h1>
            
            <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Welcome to DialysisCare</h2>
                <p className="text-gray-600">
                    Professional Hemodialysis Maintenance Tracker is loading successfully!
                </p>
                
                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-blue-900">Total Machines</h3>
                        <p className="text-2xl font-bold text-blue-600">5</p>
                    </div>
                    
                    <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-green-900">Operational</h3>
                        <p className="text-2xl font-bold text-green-600">4</p>
                    </div>
                    
                    <div className="bg-yellow-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-yellow-900">Maintenance Due</h3>
                        <p className="text-2xl font-bold text-yellow-600">1</p>
                    </div>
                </div>
                
                <div className="mt-6">
                    <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        View All Machines
                    </button>
                </div>
            </div>
        </div>
    );
};

export default HomeScreen;
