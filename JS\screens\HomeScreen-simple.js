// DialysisCare - Home Screen Component
// Dashboard with online/offline functionality

import React, { useState, useEffect } from 'react';

const HomeScreen = () => {
    console.log('HomeScreen component rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [lastUpdated, setLastUpdated] = useState(new Date());

    useEffect(() => {
        const handleOnline = () => {
            setIsOnline(true);
            setLastUpdated(new Date());
        };

        const handleOffline = () => {
            setIsOnline(false);
        };

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    return (
        <div className="p-6">
            <div className="flex items-center justify-between mb-6">
                <h1 className="text-3xl font-bold text-gray-900">
                    DialysisCare Dashboard
                </h1>

                {/* Connection Status */}
                <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    isOnline
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                }`}>
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                        isOnline ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    {isOnline ? 'Online' : 'Offline'}
                </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Welcome to DialysisCare</h2>
                <p className="text-gray-600 mb-4">
                    Professional Hemodialysis Maintenance Tracker
                </p>

                {/* Connection Status Message */}
                {!isOnline && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <div className="flex items-center">
                            <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                            </svg>
                            <div>
                                <h3 className="text-sm font-medium text-yellow-800">Limited Functionality</h3>
                                <p className="text-sm text-yellow-700">You're currently offline. Some features may not be available until you reconnect.</p>
                            </div>
                        </div>
                    </div>
                )}

                {isOnline && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <div className="flex items-center">
                            <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                            </svg>
                            <div>
                                <h3 className="text-sm font-medium text-green-800">All Systems Online</h3>
                                <p className="text-sm text-green-700">Connected and ready. Last updated: {lastUpdated.toLocaleTimeString()}</p>
                            </div>
                        </div>
                    </div>
                )}
                
                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className={`p-4 rounded-lg ${isOnline ? 'bg-blue-50' : 'bg-gray-50'}`}>
                        <h3 className={`font-semibold ${isOnline ? 'text-blue-900' : 'text-gray-600'}`}>
                            Total Machines
                        </h3>
                        <p className={`text-2xl font-bold ${isOnline ? 'text-blue-600' : 'text-gray-500'}`}>
                            {isOnline ? '5' : '5*'}
                        </p>
                        {!isOnline && <p className="text-xs text-gray-500">*Cached data</p>}
                    </div>

                    <div className={`p-4 rounded-lg ${isOnline ? 'bg-green-50' : 'bg-gray-50'}`}>
                        <h3 className={`font-semibold ${isOnline ? 'text-green-900' : 'text-gray-600'}`}>
                            Operational
                        </h3>
                        <p className={`text-2xl font-bold ${isOnline ? 'text-green-600' : 'text-gray-500'}`}>
                            {isOnline ? '4' : '4*'}
                        </p>
                        {!isOnline && <p className="text-xs text-gray-500">*Cached data</p>}
                    </div>

                    <div className={`p-4 rounded-lg ${isOnline ? 'bg-yellow-50' : 'bg-gray-50'}`}>
                        <h3 className={`font-semibold ${isOnline ? 'text-yellow-900' : 'text-gray-600'}`}>
                            Maintenance Due
                        </h3>
                        <p className={`text-2xl font-bold ${isOnline ? 'text-yellow-600' : 'text-gray-500'}`}>
                            {isOnline ? '1' : '1*'}
                        </p>
                        {!isOnline && <p className="text-xs text-gray-500">*Cached data</p>}
                    </div>
                </div>
                
                <div className="mt-6 flex flex-wrap gap-3">
                    <button
                        className={`px-4 py-2 rounded font-medium transition-colors ${
                            isOnline
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                        disabled={!isOnline}
                        title={!isOnline ? 'This feature requires an internet connection' : ''}
                    >
                        View All Machines
                    </button>

                    <button
                        className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 font-medium"
                        title="Available offline"
                    >
                        View Cached Data
                    </button>

                    {!isOnline && (
                        <button
                            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 font-medium"
                            onClick={() => window.location.reload()}
                        >
                            Retry Connection
                        </button>
                    )}

                    {/* Testing buttons for offline simulation */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                        <p className="text-sm text-gray-600 mb-2">Testing Tools:</p>
                        <div className="flex gap-2">
                            <button
                                className="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700"
                                onClick={() => {
                                    // Simulate offline
                                    window.dispatchEvent(new Event('offline'));
                                }}
                            >
                                Simulate Offline
                            </button>
                            <button
                                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                                onClick={() => {
                                    // Simulate online
                                    window.dispatchEvent(new Event('online'));
                                }}
                            >
                                Simulate Online
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HomeScreen;
