// DialysisCare - Main Application Entry Point
// Professional Hemodialysis Maintenance Tracker

import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './app.js';
import { initializeApp } from './utils/app-initializer.js';
// import { registerServiceWorker } from './utils/service-worker-registration.js'; // Temporarily disabled

// Simple offline handling function
function initializeOfflineHandling() {
    console.log('Setting up offline detection, current status:', navigator.onLine ? 'online' : 'offline');

    const offlineIndicator = document.getElementById('offline-indicator');

    function updateOfflineStatus() {
        if (navigator.onLine) {
            console.log('Device is online');
            if (offlineIndicator) {
                offlineIndicator.classList.add('hidden');
            }
        } else {
            console.log('Device is offline');
            if (offlineIndicator) {
                offlineIndicator.classList.remove('hidden');
            }
        }
    }

    // Set initial state
    updateOfflineStatus();

    // Listen for online/offline events
    window.addEventListener('online', updateOfflineStatus);
    window.addEventListener('offline', updateOfflineStatus);
}

// Initialize application
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM Content Loaded - Starting app initialization');
    try {
        // Initialize app services
        console.log('Calling initializeApp...');
        await initializeApp();
        console.log('App initialization completed');
        
        // Get root element
        console.log('Looking for root element...');
        const container = document.getElementById('root');
        if (!container) {
            throw new Error('Root element not found');
        }
        console.log('Root element found, creating React root...');

        // Create React root and render app
        const root = createRoot(container);
        console.log('Rendering App component...');
        root.render(React.createElement(App));
        console.log('App component rendered successfully');
        
        // Initialize offline handling
        console.log('Initializing offline detection...');
        initializeOfflineHandling();

        // Service worker registration temporarily disabled for debugging
        console.log('Service worker registration skipped for debugging');
        
        // Hide loading screen
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }, 1000);
        }
        
        console.log('DialysisCare application initialized successfully');
        
    } catch (error) {
        console.error('Failed to initialize DialysisCare application:', error);
        
        // Show error message to user
        const container = document.getElementById('root');
        if (container) {
            container.innerHTML = `
                <div class="min-h-screen flex items-center justify-center bg-gray-50">
                    <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <svg class="w-6 h-6 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <h1 class="text-lg font-semibold text-gray-900">Application Error</h1>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Failed to load DialysisCare. Please refresh the page or contact support if the problem persists.
                        </p>
                        <div class="bg-gray-100 rounded p-3 mb-4">
                            <code class="text-sm text-gray-800">${error.message}</code>
                        </div>
                        <button 
                            onclick="window.location.reload()" 
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
                        >
                            Reload Application
                        </button>
                    </div>
                </div>
            `;
        }
        
        // Hide loading screen
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }
});

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    
    // Track error for analytics (if implemented)
    if (window.analytics && window.analytics.track) {
        window.analytics.track('Application Error', {
            message: event.error.message,
            stack: event.error.stack,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
        });
    }
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    // Track error for analytics (if implemented)
    if (window.analytics && window.analytics.track) {
        window.analytics.track('Unhandled Promise Rejection', {
            reason: event.reason.toString()
        });
    }
});

// Performance monitoring
if ('performance' in window) {
    window.addEventListener('load', () => {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                console.log('Performance metrics:', {
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                    totalTime: perfData.loadEventEnd - perfData.fetchStart
                });
                
                // Track performance for analytics (if implemented)
                if (window.analytics && window.analytics.track) {
                    window.analytics.track('Page Load Performance', {
                        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                        loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                        totalTime: perfData.loadEventEnd - perfData.fetchStart
                    });
                }
            }
        }, 0);
    });
}

// Export for potential external use
export { App };
