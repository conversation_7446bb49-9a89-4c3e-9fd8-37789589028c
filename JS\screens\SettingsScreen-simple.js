// DialysisCare - Settings Screen with Offline Support
// Settings view with offline awareness

import React, { useState, useEffect } from 'react';

const SettingsScreen = () => {
    console.log('SettingsScreen rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    return (
        <div className="p-6">
            <h1 className="text-2xl font-bold mb-6">Settings</h1>

            {!isOnline && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <p className="text-yellow-800">
                        <strong>Limited Functionality:</strong> Some settings require an internet connection to save.
                    </p>
                </div>
            )}

            <div className="space-y-6">
                <div className="bg-white rounded-lg shadow p-6">
                    <h2 className="text-lg font-semibold mb-4">Connection Status</h2>
                    <div className="flex items-center justify-between">
                        <span>Current Status:</span>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                            {isOnline ? 'Connected' : 'Disconnected'}
                        </span>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <h2 className="text-lg font-semibold mb-4">Offline Settings</h2>
                    <p className="text-gray-600">
                        These settings are available offline and will sync when you reconnect.
                    </p>
                    <div className="mt-4">
                        <label className="flex items-center">
                            <input type="checkbox" className="mr-2" defaultChecked />
                            <span>Enable offline mode</span>
                        </label>
                        <label className="flex items-center mt-2">
                            <input type="checkbox" className="mr-2" />
                            <span>Cache machine data locally</span>
                        </label>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <h2 className="text-lg font-semibold mb-4">Online Settings</h2>
                    <p className="text-gray-600 mb-4">
                        These settings require an internet connection.
                    </p>
                    <button
                        className={`px-4 py-2 rounded font-medium ${
                            isOnline
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                        disabled={!isOnline}
                    >
                        Sync Settings to Cloud
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SettingsScreen;
