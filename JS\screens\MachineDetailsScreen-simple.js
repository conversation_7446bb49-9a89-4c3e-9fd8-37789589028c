// DialysisCare - Machine Details Screen with Offline Support
// Machine details view with offline awareness

import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';

const MachineDetailsScreen = () => {
    console.log('MachineDetailsScreen rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const { id } = useParams();

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    // Simulated cached machine data (would normally come from localStorage or IndexedDB)
    const getMachineData = (machineId) => {
        const machineDatabase = {
            'HD-001': {
                id: 'HD-001',
                name: 'Hemodialysis Unit 1',
                model: 'Fresenius 5008S',
                serialNumber: 'FS5008S-2024-001',
                status: 'operational',
                location: 'Room A1',
                installDate: '2023-01-15',
                lastMaintenance: '2024-06-15',
                nextMaintenance: '2024-07-15',
                operatingHours: 2847,
                totalTreatments: 1456,
                lastCleaning: '2024-07-03',
                waterQuality: 'Excellent',
                dialysateFlow: '500 mL/min',
                bloodFlow: '350 mL/min',
                alerts: [],
                maintenanceHistory: [
                    { date: '2024-06-15', type: 'Routine Maintenance', technician: 'John Smith', status: 'Completed' },
                    { date: '2024-05-15', type: 'Filter Replacement', technician: 'Sarah Johnson', status: 'Completed' }
                ]
            },
            'HD-002': {
                id: 'HD-002',
                name: 'Hemodialysis Unit 2',
                model: 'Fresenius 5008S',
                serialNumber: 'FS5008S-2024-002',
                status: 'maintenance',
                location: 'Room A2',
                installDate: '2023-01-20',
                lastMaintenance: '2024-06-10',
                nextMaintenance: '2024-07-04',
                operatingHours: 2923,
                totalTreatments: 1523,
                lastCleaning: '2024-07-02',
                waterQuality: 'Good',
                dialysateFlow: '500 mL/min',
                bloodFlow: '300 mL/min',
                alerts: [
                    { type: 'warning', message: 'Maintenance due today', priority: 'high' },
                    { type: 'info', message: 'Filter replacement scheduled', priority: 'medium' }
                ],
                maintenanceHistory: [
                    { date: '2024-06-10', type: 'Routine Maintenance', technician: 'John Smith', status: 'In Progress' },
                    { date: '2024-05-10', type: 'Pump Inspection', technician: 'Sarah Johnson', status: 'Completed' }
                ]
            },
            'HD-003': {
                id: 'HD-003',
                name: 'Hemodialysis Unit 3',
                model: 'Fresenius 5008S',
                serialNumber: 'FS5008S-2024-003',
                status: 'operational',
                location: 'Room B1',
                installDate: '2023-02-01',
                lastMaintenance: '2024-06-20',
                nextMaintenance: '2024-07-20',
                operatingHours: 2756,
                totalTreatments: 1398,
                lastCleaning: '2024-07-03',
                waterQuality: 'Excellent',
                dialysateFlow: '500 mL/min',
                bloodFlow: '400 mL/min',
                alerts: [],
                maintenanceHistory: [
                    { date: '2024-06-20', type: 'Routine Maintenance', technician: 'Mike Wilson', status: 'Completed' },
                    { date: '2024-05-20', type: 'Software Update', technician: 'Tech Support', status: 'Completed' }
                ]
            },
            'HD-004': {
                id: 'HD-004',
                name: 'Hemodialysis Unit 4',
                model: 'Fresenius 5008S',
                serialNumber: 'FS5008S-2024-004',
                status: 'operational',
                location: 'Room B2',
                installDate: '2023-02-10',
                lastMaintenance: '2024-06-18',
                nextMaintenance: '2024-07-18',
                operatingHours: 2834,
                totalTreatments: 1445,
                lastCleaning: '2024-07-03',
                waterQuality: 'Good',
                dialysateFlow: '500 mL/min',
                bloodFlow: '375 mL/min',
                alerts: [
                    { type: 'info', message: 'Routine cleaning completed', priority: 'low' }
                ],
                maintenanceHistory: [
                    { date: '2024-06-18', type: 'Routine Maintenance', technician: 'Sarah Johnson', status: 'Completed' },
                    { date: '2024-05-18', type: 'Pressure Test', technician: 'John Smith', status: 'Completed' }
                ]
            },
            'HD-005': {
                id: 'HD-005',
                name: 'Hemodialysis Unit 5',
                model: 'Fresenius 5008S',
                serialNumber: 'FS5008S-2024-005',
                status: 'offline',
                location: 'Room C1',
                installDate: '2023-02-15',
                lastMaintenance: '2024-06-05',
                nextMaintenance: '2024-07-05',
                operatingHours: 2654,
                totalTreatments: 1334,
                lastCleaning: '2024-07-01',
                waterQuality: 'Needs Testing',
                dialysateFlow: 'N/A',
                bloodFlow: 'N/A',
                alerts: [
                    { type: 'error', message: 'Machine offline - requires inspection', priority: 'critical' },
                    { type: 'warning', message: 'Maintenance overdue', priority: 'high' }
                ],
                maintenanceHistory: [
                    { date: '2024-06-05', type: 'Emergency Repair', technician: 'Mike Wilson', status: 'Pending' },
                    { date: '2024-05-05', type: 'Routine Maintenance', technician: 'John Smith', status: 'Completed' }
                ]
            }
        };

        return machineDatabase[machineId] || null;
    };

    const machineData = getMachineData(id);
    const lastSyncTime = new Date(Date.now() - (isOnline ? 0 : 1000 * 60 * 15)); // 15 minutes ago if offline

    return (
        <div className="p-6">
            {/* Breadcrumb Navigation */}
            <nav className="flex mb-4" aria-label="Breadcrumb">
                <ol className="inline-flex items-center space-x-1 md:space-x-3">
                    <li className="inline-flex items-center">
                        <Link to="/" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                            Dashboard
                        </Link>
                    </li>
                    <li>
                        <div className="flex items-center">
                            <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                            </svg>
                            <Link to="/machines" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                                Machines
                            </Link>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div className="flex items-center">
                            <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                            </svg>
                            <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">{id || 'Machine Details'}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-bold">Machine Details - {id}</h1>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                    {isOnline ? 'Live Data' : 'Cached Data'}
                </div>
            </div>

            {!isOnline && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                        <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                        </svg>
                        <div>
                            <h3 className="text-sm font-medium text-yellow-800">Offline Mode - Cached Data</h3>
                            <p className="text-sm text-yellow-700">
                                Last synchronized: {lastSyncTime.toLocaleString()}. 
                                Connect to internet for real-time updates.
                            </p>
                        </div>
                    </div>
                </div>
            )}

            {!machineData ? (
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="text-center">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">Machine Not Found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            No data available for machine ID: {id}
                        </p>
                        <div className="mt-6">
                            <Link to="/machines" className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                Back to Machines
                            </Link>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="space-y-6">
                    {/* Machine Status and Alerts */}
                    {machineData.alerts && machineData.alerts.length > 0 && (
                        <div className="bg-white rounded-lg shadow p-6">
                            <h2 className="text-lg font-semibold mb-4 flex items-center">
                                <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                                </svg>
                                Active Alerts
                            </h2>
                            <div className="space-y-2">
                                {machineData.alerts.map((alert, index) => (
                                    <div key={index} className={`p-3 rounded-lg border ${
                                        alert.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
                                        alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' :
                                        'bg-blue-50 border-blue-200 text-blue-800'
                                    }`}>
                                        <div className="flex items-center">
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mr-3 ${
                                                alert.priority === 'critical' ? 'bg-red-100 text-red-800' :
                                                alert.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                                                alert.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                                {alert.priority?.toUpperCase()}
                                            </span>
                                            <span className="font-medium">{alert.message}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Machine Overview */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between mb-6">
                            <h2 className="text-lg font-semibold">Machine Overview</h2>
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                machineData.status === 'operational' ? 'bg-green-100 text-green-800' :
                                machineData.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                            }`}>
                                <div className={`w-2 h-2 rounded-full mr-2 ${
                                    machineData.status === 'operational' ? 'bg-green-500' :
                                    machineData.status === 'maintenance' ? 'bg-yellow-500' :
                                    'bg-red-500'
                                }`}></div>
                                {machineData.status.charAt(0).toUpperCase() + machineData.status.slice(1)}
                            </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div>
                                <h3 className="text-sm font-medium text-gray-500 mb-1">Machine ID</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.id}</p>
                            </div>
                            <div>
                                <h3 className="text-sm font-medium text-gray-500 mb-1">Model</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.model}</p>
                            </div>
                            <div>
                                <h3 className="text-sm font-medium text-gray-500 mb-1">Serial Number</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.serialNumber}</p>
                            </div>
                            <div>
                                <h3 className="text-sm font-medium text-gray-500 mb-1">Location</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.location}</p>
                            </div>
                            <div>
                                <h3 className="text-sm font-medium text-gray-500 mb-1">Install Date</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.installDate}</p>
                            </div>
                            <div>
                                <h3 className="text-sm font-medium text-gray-500 mb-1">Operating Hours</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.operatingHours.toLocaleString()} hrs</p>
                            </div>
                        </div>
                    </div>

                    {/* Performance Metrics */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-lg font-semibold mb-6">Performance Metrics</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div className="text-center">
                                <div className="text-3xl font-bold text-blue-600">{machineData.totalTreatments.toLocaleString()}</div>
                                <div className="text-sm text-gray-500 mt-1">Total Treatments</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-green-600">{machineData.waterQuality}</div>
                                <div className="text-sm text-gray-500 mt-1">Water Quality</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-purple-600">{machineData.dialysateFlow}</div>
                                <div className="text-sm text-gray-500 mt-1">Dialysate Flow</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-orange-600">{machineData.bloodFlow}</div>
                                <div className="text-sm text-gray-500 mt-1">Blood Flow</div>
                            </div>
                        </div>
                    </div>

                    {/* Maintenance Information */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-lg font-semibold mb-6">Maintenance Schedule</h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h3 className="text-sm font-medium text-gray-500 mb-2">Last Maintenance</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.lastMaintenance}</p>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h3 className="text-sm font-medium text-gray-500 mb-2">Next Maintenance</h3>
                                <p className={`text-lg font-semibold ${
                                    new Date(machineData.nextMaintenance) <= new Date() ? 'text-red-600' : 'text-gray-900'
                                }`}>
                                    {machineData.nextMaintenance}
                                </p>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h3 className="text-sm font-medium text-gray-500 mb-2">Last Cleaning</h3>
                                <p className="text-lg font-semibold text-gray-900">{machineData.lastCleaning}</p>
                            </div>
                        </div>
                    </div>

                    {/* Maintenance History */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-lg font-semibold mb-6">Recent Maintenance History</h2>
                        <div className="space-y-4">
                            {machineData.maintenanceHistory.map((record, index) => (
                                <div key={index} className="border-l-4 border-blue-200 pl-4 py-2">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h3 className="font-medium text-gray-900">{record.type}</h3>
                                            <p className="text-sm text-gray-500">
                                                {record.date} • Technician: {record.technician}
                                            </p>
                                        </div>
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                            record.status === 'Completed' ? 'bg-green-100 text-green-800' :
                                            record.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-gray-100 text-gray-800'
                                        }`}>
                                            {record.status}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-lg font-semibold mb-4">Actions</h2>
                        <div className="flex flex-wrap gap-3">
                            <button
                                className={`px-4 py-2 rounded font-medium transition-colors ${
                                    isOnline
                                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                                disabled={!isOnline}
                                title={!isOnline ? 'This feature requires an internet connection' : ''}
                            >
                                Schedule Maintenance
                            </button>

                            <button
                                className={`px-4 py-2 rounded font-medium transition-colors ${
                                    isOnline
                                        ? 'bg-green-600 text-white hover:bg-green-700'
                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                                disabled={!isOnline}
                                title={!isOnline ? 'This feature requires an internet connection' : ''}
                            >
                                Log Treatment
                            </button>

                            <Link
                                to={`/machine/${id}/log`}
                                className="px-4 py-2 bg-purple-600 text-white rounded font-medium hover:bg-purple-700 transition-colors"
                            >
                                View Full Log
                            </Link>

                            <button className="px-4 py-2 bg-gray-600 text-white rounded font-medium hover:bg-gray-700 transition-colors">
                                Export Data {!isOnline && '(Cached)'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default MachineDetailsScreen;
