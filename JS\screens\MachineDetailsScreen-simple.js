// DialysisCare - Machine Details Screen with Offline Support
// Machine details view with offline awareness

import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';

const MachineDetailsScreen = () => {
    console.log('MachineDetailsScreen rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const { id } = useParams();

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    return (
        <div className="p-6">
            {/* Breadcrumb Navigation */}
            <nav className="flex mb-4" aria-label="Breadcrumb">
                <ol className="inline-flex items-center space-x-1 md:space-x-3">
                    <li className="inline-flex items-center">
                        <Link to="/" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                            Dashboard
                        </Link>
                    </li>
                    <li>
                        <div className="flex items-center">
                            <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                            </svg>
                            <Link to="/machines" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                                Machines
                            </Link>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div className="flex items-center">
                            <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                            </svg>
                            <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">{id || 'Machine Details'}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-bold">Machine Details - {id}</h1>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                    {isOnline ? 'Live Data' : 'Cached Data'}
                </div>
            </div>

            {!isOnline && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <p className="text-yellow-800">
                        <strong>Offline Mode:</strong> Showing cached machine data. Connect to internet for real-time updates.
                    </p>
                </div>
            )}

            <div className="bg-white rounded-lg shadow p-6">
                <p className="text-gray-600">
                    Machine details will be displayed here.
                    {!isOnline && ' (Currently showing cached information)'}
                </p>
            </div>
        </div>
    );
};

export default MachineDetailsScreen;
