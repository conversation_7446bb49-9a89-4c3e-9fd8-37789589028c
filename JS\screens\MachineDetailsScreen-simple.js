// DialysisCare - Machine Details Screen with Offline Support
// Machine details view with offline awareness

import React, { useState, useEffect } from 'react';

const MachineDetailsScreen = () => {
    console.log('MachineDetailsScreen rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    return (
        <div className="p-6">
            <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-bold">Machine Details</h1>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                    {isOnline ? 'Live Data' : 'Cached Data'}
                </div>
            </div>

            {!isOnline && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <p className="text-yellow-800">
                        <strong>Offline Mode:</strong> Showing cached machine data. Connect to internet for real-time updates.
                    </p>
                </div>
            )}

            <div className="bg-white rounded-lg shadow p-6">
                <p className="text-gray-600">
                    Machine details will be displayed here.
                    {!isOnline && ' (Currently showing cached information)'}
                </p>
            </div>
        </div>
    );
};

export default MachineDetailsScreen;
