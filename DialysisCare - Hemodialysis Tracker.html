<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DialysisCare - Hemodialysis Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .fade-in { animation: fadeIn 0.5s ease-in-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .machine-card {
             transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
        }
        .machine-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        /* Hide scrollbar but allow scrolling */
        .no-scrollbar::-webkit-scrollbar { display: none; }
        .no-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">

    <!-- Header -->
    <header class="bg-blue-600 dark:bg-blue-800 text-white py-4 px-6 shadow-md flex justify-between items-center sticky top-0 z-30">
        <h1 class="text-2xl font-bold flex items-center">
            <i class="fas fa-heart-pulse mr-3"></i>
            <span data-en="DialysisCare" data-ar="دياليسيس كير">DialysisCare</span>
        </h1>
        <div class="flex items-center gap-2">
            <button id="langSwitch" class="px-4 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-900 transition-colors">
                <i class="fas fa-language mr-1"></i>
                <span id="langSwitchText" data-en="العربية" data-ar="English">العربية</span>
            </button>
            <button id="homeAddMachineBtn" class="bg-white text-blue-600 px-4 py-2 rounded-lg shadow font-semibold hover:bg-gray-100 transition-transform transform hover:scale-105">
                <i class="fas fa-plus mr-2"></i>
                <span data-en="Add Machine" data-ar="إضافة جهاز">Add Machine</span>
            </button>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="p-4 sm:p-6 lg:p-8">

        <!-- Home Screen (Machine List) -->
        <div id="homeScreen" class="fade-in">
            <div class="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div class="flex flex-col md:flex-row gap-4 items-end">
                    <div class="flex-1">
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Search" data-ar="بحث">Search</label>
                        <div class="relative">
                             <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-search text-gray-400"></i>
                            </span>
                            <input type="text" id="search" data-placeholder-en="Search by model, serial, or location..." data-placeholder-ar="...ابحث بالموديل، الرقم التسلسلي، أو الموقع" placeholder="Search by model, serial, or location..." class="w-full p-3 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none">
                        </div>
                    </div>
                    <div class="w-full md:w-56">
                        <label for="filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Filter by Status" data-ar="تصفية حسب الحالة">Filter by Status</label>
                        <select id="filter" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:outline-none">
                            <option value="all" data-en="All Machines" data-ar="كل الأجهزة">All Machines</option>
                            <option value="needs_maintenance" data-en="Needs Maintenance" data-ar="تحتاج صيانة">Needs Maintenance</option>
                            <option value="due_soon" data-en="Service Due Soon" data-ar="صيانة قريبة">Service Due Soon</option>
                            <option value="recently_serviced" data-en="Recently Serviced" data-ar="تمت صيانتها مؤخراً">Recently Serviced</option>
                        </select>
                    </div>
                </div>
            </div>

            <div id="machinesList" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- Machine cards will be dynamically inserted here -->
            </div>
            <div id="loadingState" class="text-center py-10">
                <i class="fas fa-spinner fa-spin text-4xl text-blue-500"></i>
                <p class="mt-4 text-lg" data-en="Loading machines..." data-ar="...جاري تحميل الأجهزة">Loading machines...</p>
            </div>
            <div id="emptyState" class="hidden text-center py-16 px-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                 <i class="fas fa-server text-5xl text-gray-400 mb-4"></i>
                 <h3 class="text-2xl font-semibold mb-2" data-en="No Machines Found" data-ar="لم يتم العثور على أجهزة">No Machines Found</h3>
                 <p class="text-gray-500 dark:text-gray-400 mb-4" data-en="Get started by adding a new hemodialysis machine." data-ar="ابدأ بإضافة جهاز غسيل كلوي جديد">Get started by adding a new hemodialysis machine.</p>
                 <button id="addFirstMachineBtn" class="bg-blue-600 text-white px-6 py-3 rounded-lg shadow font-semibold hover:bg-blue-700 transition-transform transform hover:scale-105">
                    <i class="fas fa-plus mr-2"></i>
                    <span data-en="Add a Machine" data-ar="إضافة جهاز">Add a Machine</span>
                </button>
            </div>
        </div>

        <!-- Machine Details Screen -->
        <div id="machineDetailsScreen" class="hidden">
            <!-- Details will be dynamically inserted here -->
        </div>

        <!-- Add/Edit Machine Screen -->
        <div id="machineFormScreen" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden p-6 max-w-4xl mx-auto">
                <div class="flex justify-between items-center mb-6">
                    <h2 id="machineFormTitle" class="text-2xl font-bold text-gray-800 dark:text-white flex items-center"></h2>
                    <button class="go-back-btn text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <form id="machineForm" class="space-y-4">
                    <input type="hidden" id="machineId" name="id">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Model" data-ar="الموديل">Model *</label>
                            <input type="text" id="model" name="model" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700" required />
                        </div>
                        <div>
                            <label for="serial_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Serial Number" data-ar="الرقم التسلسلي">Serial Number *</label>
                            <input type="text" id="serial_number" name="serial_number" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700" required />
                        </div>
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Location" data-ar="الموقع">Location *</label>
                            <input type="text" id="location" name="location" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700" required />
                        </div>
                        <div>
                            <label for="last_maintenance_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Last Maintenance Date" data-ar="تاريخ آخر صيانة">Last Maintenance Date</label>
                            <input type="date" id="last_maintenance_date" name="last_maintenance_date" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700" />
                        </div>
                        <div class="md:col-span-2">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Notes" data-ar="ملاحظات">Notes</label>
                            <textarea id="notes" name="notes" rows="3" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700"></textarea>
                        </div>
                    </div>
                    <div class="flex justify-end gap-4 pt-4">
                        <button type="button" class="go-back-btn bg-gray-200 dark:bg-gray-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-500" data-en="Cancel" data-ar="إلغاء">Cancel</button>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700" data-en="Save Machine" data-ar="حفظ الجهاز">Save Machine</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Add/Edit Log Screen -->
        <div id="logFormScreen" class="hidden">
             <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden p-6 max-w-4xl mx-auto">
                <div class="flex justify-between items-center mb-6">
                    <h2 id="logFormTitle" class="text-2xl font-bold text-gray-800 dark:text-white flex items-center"></h2>
                     <button class="go-back-log-btn text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <form id="logForm" class="space-y-4">
                    <input type="hidden" id="logMachineId">
                    <input type="hidden" id="logId">
                     <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="log_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Date" data-ar="التاريخ">Date *</label>
                            <input type="date" id="log_date" name="date" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700" required>
                        </div>
                        <div>
                            <label for="log_technician" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Technician" data-ar="الفني">Technician *</label>
                            <input type="text" id="log_technician" name="technician" data-placeholder-en="Technician Name" data-placeholder-ar="اسم الفني" placeholder="Technician Name" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700" required>
                        </div>
                        <div class="md:col-span-2">
                            <label for="log_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-en="Description" data-ar="الوصف">Description *</label>
                            <textarea id="log_description" name="description" rows="4" data-placeholder-en="Description of work performed..." data-placeholder-ar="...وصف العمل المنجز" placeholder="Description of work performed..." class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700" required></textarea>
                        </div>
                    </div>
                    <div class="flex justify-end gap-4 pt-4">
                        <button type="button" class="go-back-log-btn bg-gray-200 dark:bg-gray-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-500" data-en="Cancel" data-ar="إلغاء">Cancel</button>
                        <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-green-700" data-en="Save Log" data-ar="حفظ السجل">Save Log</button>
                    </div>
                </form>
            </div>
        </div>

    </main>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-sm">
            <div class="p-6 text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-xl font-bold mb-2" data-en="Are you sure?" data-ar="هل أنت متأكد؟">Are you sure?</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6" data-en="This action cannot be undone. All data for this machine will be permanently deleted." data-ar="لا يمكن التراجع عن هذا الإجراء. سيتم حذف جميع بيانات هذا الجهاز بشكل دائم">This action cannot be undone. All data for this machine will be permanently deleted.</p>
                <div class="flex justify-center gap-4">
                    <button id="cancelDeleteBtn" class="bg-gray-200 dark:bg-gray-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-500" data-en="Cancel" data-ar="إلغاء">Cancel</button>
                    <button id="confirmDeleteBtn" class="bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700" data-en="Delete" data-ar="حذف">Delete</button>
                </div>
            </div>
        </div>
    </div>


    <script type="module">
        // Firebase Imports
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, collection, doc, addDoc, getDocs, onSnapshot, updateDoc, deleteDoc, query, where, setDoc, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // --- App State ---
        let db, auth;
        let userId;
        let allMachines = [];
        let currentLanguage = 'en';
        let currentScreen = 'homeScreen';
        let currentMachineId = null;
        let unsubscribeMachines = null;
        let unsubscribeLogs = null;
        let machineToDeleteId = null;
        const SERVICE_INTERVAL_RECENT = 30; // days
        const SERVICE_INTERVAL_DUE_SOON = 60; // days
        const SERVICE_INTERVAL_NEEDS = 90; // days

        // --- UI Elements ---
        const screens = {
            home: document.getElementById('homeScreen'),
            details: document.getElementById('machineDetailsScreen'),
            machineForm: document.getElementById('machineFormScreen'),
            logForm: document.getElementById('logFormScreen'),
        };
        const searchEl = document.getElementById('search');
        const filterEl = document.getElementById('filter');
        const loadingStateEl = document.getElementById('loadingState');
        const emptyStateEl = document.getElementById('emptyState');
        const deleteConfirmModalEl = document.getElementById('deleteConfirmModal');

        // --- Firebase Config ---
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'dialysis-care-app';
        const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : { apiKey: "your-api-key", authDomain: "your-project-id.firebaseapp.com", projectId: "your-project-id" };

        // --- Initialization ---
        async function main() {
            const app = initializeApp(firebaseConfig);
            db = getFirestore(app);
            auth = getAuth(app);

            onAuthStateChanged(auth, async (user) => {
                if (user) {
                    userId = user.uid;
                    console.log("User authenticated with UID:", userId);
                    attachMachineListener();
                } else {
                    try {
                        if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
                            await signInWithCustomToken(auth, __initial_auth_token);
                        } else {
                            await signInAnonymously(auth);
                        }
                    } catch (error) {
                        console.error("Authentication failed:", error);
                        loadingStateEl.innerText = "Authentication Failed.";
                    }
                }
            });
        }

        // --- Screen Navigation ---
        function showScreen(screenName) {
            currentScreen = screenName;
            Object.values(screens).forEach(screen => screen.classList.add('hidden'));
            if (screens[screenName]) {
                screens[screenName].classList.remove('hidden');
                screens[screenName].classList.add('fade-in');
            }
            window.scrollTo(0, 0);
        }

        // --- Data Fetching & Rendering ---

        function attachMachineListener() {
            if (unsubscribeMachines) unsubscribeMachines();
            const machinesCollection = collection(db, `artifacts/${appId}/public/data/machines`);
            unsubscribeMachines = onSnapshot(machinesCollection, (snapshot) => {
                loadingStateEl.classList.add('hidden');
                allMachines = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                filterAndRenderMachines();
                updateEmptyState();
            }, (error) => {
                console.error("Error fetching machines:", error);
                loadingStateEl.innerHTML = `<p class="text-red-500">Error loading data.</p>`;
            });
        }

        function filterAndRenderMachines() {
            const searchTerm = searchEl.value.toLowerCase();
            const filterValue = filterEl.value;

            let filtered = allMachines.filter(m =>
                m.model.toLowerCase().includes(searchTerm) ||
                m.serial_number.toLowerCase().includes(searchTerm) ||
                m.location.toLowerCase().includes(searchTerm)
            );

            if (filterValue !== 'all') {
                filtered = filtered.filter(m => {
                    const days = m.last_maintenance_date ? Math.floor((new Date() - new Date(m.last_maintenance_date)) / (1000 * 60 * 60 * 24)) : Infinity;
                    if (filterValue === 'needs_maintenance') return days > SERVICE_INTERVAL_NEEDS;
                    if (filterValue === 'due_soon') return days > SERVICE_INTERVAL_DUE_SOON && days <= SERVICE_INTERVAL_NEEDS;
                    if (filterValue === 'recently_serviced') return days <= SERVICE_INTERVAL_RECENT;
                    return false;
                });
            }
            renderMachineCards(filtered);
        }

        function renderMachineCards(machinesToRender) {
            const listEl = document.getElementById('machinesList');
            listEl.innerHTML = '';
            if (machinesToRender.length === 0 && allMachines.length > 0) {
                listEl.innerHTML = `<p class="col-span-full text-center text-gray-500" data-en="No machines match your search or filter." data-ar="لا توجد أجهزة تطابق بحثك أو تصفيتك.">No machines match your search or filter.</p>`;
            } else {
                machinesToRender.forEach(m => {
                    const daysSinceService = m.last_maintenance_date ? Math.floor((new Date() - new Date(m.last_maintenance_date)) / (1000 * 60 * 60 * 24)) : null;
                    
                    let status = {
                        textEn: 'OK', textAr: 'جيد', color: 'green',
                        progress: 100, nextServiceTextEn: 'N/A', nextServiceTextAr: 'غير متاح'
                    };

                    if (daysSinceService !== null) {
                        const nextServiceIn = SERVICE_INTERVAL_NEEDS - daysSinceService;
                        status.progress = Math.max(0, Math.min(100, (nextServiceIn / SERVICE_INTERVAL_NEEDS) * 100));

                        if (daysSinceService > SERVICE_INTERVAL_NEEDS) {
                            status = { textEn: 'Service Overdue', textAr: 'الصيانة متأخرة', color: 'red', progress: 0, nextServiceTextEn: `Overdue by ${daysSinceService - SERVICE_INTERVAL_NEEDS} days`, nextServiceTextAr: `متأخرة ${daysSinceService - SERVICE_INTERVAL_NEEDS} يوم` };
                        } else if (daysSinceService > SERVICE_INTERVAL_DUE_SOON) {
                            status = { textEn: 'Service Due', textAr: 'الصيانة قريبة', color: 'yellow', progress: status.progress, nextServiceTextEn: `Due in ${nextServiceIn} days`, nextServiceTextAr: `مستحقة بعد ${nextServiceIn} يوم` };
                        } else {
                             status = { textEn: 'OK', textAr: 'جيد', color: 'green', progress: status.progress, nextServiceTextEn: `Due in ${nextServiceIn} days`, nextServiceTextAr: `مستحقة بعد ${nextServiceIn} يوم` };
                        }
                    }

                    const card = document.createElement('div');
                    card.className = 'machine-card bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-transparent hover:border-blue-400 dark:hover:border-blue-600 transition-all duration-300 cursor-pointer';
                    card.innerHTML = `
                        <div class="p-5">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">${m.model}</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">SN: ${m.serial_number}</p>
                                </div>
                                <span class="bg-${status.color}-100 text-${status.color}-800 text-xs font-medium px-2.5 py-1 rounded-full dark:bg-${status.color}-900 dark:text-${status.color}-300" data-en="${status.textEn}" data-ar="${status.textAr}">${status.textEn}</span>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                                    <i class="fas fa-map-marker-alt mr-2 text-blue-500"></i>
                                    <span>${m.location}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-wrench mr-2 text-blue-500"></i>
                                    <span data-en="Last Service:" data-ar=":آخر صيانة">Last Service:</span>&nbsp;
                                    <span data-en="${daysSinceService !== null ? `${daysSinceService} days ago` : 'N/A'}" data-ar="${daysSinceService !== null ? `قبل ${daysSinceService} يوم` : 'لا يوجد'}">${daysSinceService !== null ? `${daysSinceService} days ago` : 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700/50 px-5 py-3 border-t border-gray-100 dark:border-gray-700 flex justify-between items-center gap-4">
                            <span class="text-xs text-gray-500 dark:text-gray-400" data-en="${status.nextServiceTextEn}" data-ar="${status.nextServiceTextAr}">${status.nextServiceTextEn}</span>
                            <div class="w-24 h-2 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                                <div class="h-full bg-${status.color}-500" style="width: ${status.progress}%"></div>
                            </div>
                        </div>`;
                    card.addEventListener('click', () => showMachineDetails(m.id));
                    listEl.appendChild(card);
                });
            }
            translateAll();
        }

        function showMachineDetails(machineId) {
            currentMachineId = machineId;
            const machine = allMachines.find(m => m.id === machineId);
            if (!machine) return;

            if (unsubscribeLogs) unsubscribeLogs();
            const logsCollection = collection(db, `artifacts/${appId}/public/data/machines/${machine.id}/logs`);
            unsubscribeLogs = onSnapshot(logsCollection, (snapshot) => {
                const logs = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                renderMachineDetails(machine, logs);
            });
        }
        
        function renderMachineDetails(machine, logs) {
            const detailsContainer = screens.details;
            const daysSinceService = machine.last_maintenance_date ? Math.floor((new Date() - new Date(machine.last_maintenance_date)) / (1000 * 60 * 60 * 24)) : null;
            
            let status = { textEn: 'Active', textAr: 'نشط', color: 'green' };
             if (daysSinceService !== null && daysSinceService > SERVICE_INTERVAL_NEEDS) {
                status = { textEn: 'Needs Service', textAr: 'يحتاج صيانة', color: 'red' };
            } else if (daysSinceService !== null && daysSinceService > SERVICE_INTERVAL_DUE_SOON) {
                status = { textEn: 'Service Due', textAr: 'صيانة قريبة', color: 'yellow' };
            }

            detailsContainer.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <button class="go-back-btn text-blue-600 dark:text-blue-400 hover:text-blue-800 flex items-center mb-4">
                                    <i class="fas fa-arrow-left mr-2"></i> <span data-en="Back to List" data-ar="العودة للقائمة">Back to List</span>
                                </button>
                                <h2 class="text-3xl font-bold text-gray-800 dark:text-white">${machine.model}</h2>
                                <p class="text-gray-600 dark:text-gray-400">SN: ${machine.serial_number}</p>
                            </div>
                            <div class="flex space-x-2">
                                <button id="editMachineDetailBtn" class="p-2 h-10 w-10 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900 rounded-full"><i class="fas fa-edit"></i></button>
                                <button id="deleteMachineDetailBtn" class="p-2 h-10 w-10 text-red-600 hover:bg-red-100 dark:hover:bg-red-900 rounded-full"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Basic Info -->
                            <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-3 flex items-center"><i class="fas fa-info-circle mr-2"></i> <span data-en="Basic Information" data-ar="المعلومات الأساسية">Basic Information</span></h3>
                                <div class="space-y-3 text-sm">
                                    <p><strong class="text-gray-500 dark:text-gray-400" data-en="Location:" data-ar=":الموقع">Location:</strong> ${machine.location}</p>
                                    <p><strong class="text-gray-500 dark:text-gray-400" data-en="Status:" data-ar=":الحالة">Status:</strong> <span class="bg-${status.color}-100 text-${status.color}-800 px-2 py-1 rounded-full text-xs dark:bg-${status.color}-900 dark:text-${status.color}-300" data-en="${status.textEn}" data-ar="${status.textAr}">${status.textEn}</span></p>
                                </div>
                            </div>
                            <!-- Maintenance -->
                            <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                                 <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-3 flex items-center"><i class="fas fa-calendar-check mr-2"></i> <span data-en="Maintenance" data-ar="الصيانة">Maintenance</span></h3>
                                 <div class="space-y-3 text-sm">
                                     <p><strong class="text-gray-500 dark:text-gray-400" data-en="Last Service:" data-ar=":آخر صيانة">Last Service:</strong> ${machine.last_maintenance_date || 'N/A'}</p>
                                     <p><strong class="text-gray-500 dark:text-gray-400" data-en="Days Since Service:" data-ar=":أيام منذ آخر صيانة">Days Since Service:</strong> ${daysSinceService !== null ? daysSinceService : 'N/A'}</p>
                                 </div>
                            </div>
                        </div>
                         <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-3"><i class="fas fa-clipboard mr-2"></i> <span data-en="Notes" data-ar="ملاحظات">Notes</span></h3>
                            <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg text-sm"><p>${machine.notes || '...'}</p></div>
                        </div>

                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-xl font-semibold text-gray-800 dark:text-white flex items-center"><i class="fas fa-history mr-2"></i> <span data-en="Maintenance History" data-ar="سجل الصيانة">Maintenance History</span></h3>
                            <button id="addLogBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                                <i class="fas fa-plus mr-2"></i> <span data-en="Add Log" data-ar="إضافة سجل">Add Log</span>
                            </button>
                        </div>
                        <div id="detailLogList" class="space-y-3">
                            <!-- Logs will be rendered here -->
                        </div>
                    </div>
                </div>`;
            
            const logListContainer = detailsContainer.querySelector('#detailLogList');
            if (logs.length === 0) {
                logListContainer.innerHTML = `<div class="text-center py-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg"><p class="text-gray-500 dark:text-gray-400" data-en="No maintenance logs yet." data-ar="لا توجد سجلات صيانة حتى الآن.">No maintenance logs yet.</p></div>`;
            } else {
                logListContainer.innerHTML = '';
                 logs.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(log => {
                    const logEl = document.createElement('div');
                    logEl.className = 'bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow';
                    logEl.innerHTML = `
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="font-medium text-gray-800 dark:text-white">${log.description.substring(0, 50)}...</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">${new Date(log.date).toLocaleDateString()}</p>
                            </div>
                             <div class="text-sm text-gray-500 dark:text-gray-300 flex items-center"><i class="fas fa-user mr-2"></i>${log.technician}</div>
                        </div>
                    `;
                    logListContainer.appendChild(logEl);
                });
            }
            
            detailsContainer.querySelector('#editMachineDetailBtn').addEventListener('click', () => showMachineForm(machine));
            detailsContainer.querySelector('#deleteMachineDetailBtn').addEventListener('click', () => openDeleteConfirm(machine.id));
            detailsContainer.querySelector('#addLogBtn').addEventListener('click', () => showLogForm(machine.id));
            detailsContainer.querySelectorAll('.go-back-btn').forEach(btn => btn.addEventListener('click', () => showScreen('home')));
            
            translateAll();
            showScreen('details');
        }

        function updateEmptyState() {
            if (allMachines.length === 0) {
                emptyStateEl.classList.remove('hidden');
                document.getElementById('machinesList').classList.add('hidden');
            } else {
                emptyStateEl.classList.add('hidden');
                document.getElementById('machinesList').classList.remove('hidden');
            }
        }

        // --- Form Management ---
        function showMachineForm(machine = null) {
            const form = document.getElementById('machineForm');
            form.reset();
            const titleEl = document.getElementById('machineFormTitle');
            if (machine) {
                titleEl.innerHTML = `<i class="fas fa-edit mr-2 text-blue-600"></i> <span data-en="Edit Machine" data-ar="تعديل الجهاز">Edit Machine</span>`;
                document.getElementById('machineId').value = machine.id;
                document.getElementById('model').value = machine.model;
                document.getElementById('serial_number').value = machine.serial_number;
                document.getElementById('location').value = machine.location;
                document.getElementById('last_maintenance_date').value = machine.last_maintenance_date || '';
                document.getElementById('notes').value = machine.notes || '';
            } else {
                titleEl.innerHTML = `<i class="fas fa-plus-circle mr-2 text-blue-600"></i> <span data-en="Add New Machine" data-ar="إضافة جهاز جديد">Add New Machine</span>`;
                document.getElementById('machineId').value = '';
            }
            translateAll();
            showScreen('machineForm');
        }
        
        function showLogForm(machineId, log = null) {
            const form = document.getElementById('logForm');
            form.reset();
            const titleEl = document.getElementById('logFormTitle');
            document.getElementById('logMachineId').value = machineId;
            if (log) {
                // Editing not implemented in this version for simplicity, but structure is here
                titleEl.innerHTML = `<i class="fas fa-edit mr-2 text-green-600"></i> <span data-en="Edit Log" data-ar="تعديل السجل">Edit Log</span>`;
                document.getElementById('logId').value = log.id;
                // populate fields...
            } else {
                titleEl.innerHTML = `<i class="fas fa-clipboard-list mr-2 text-green-600"></i> <span data-en="Add Maintenance Log" data-ar="إضافة سجل صيانة">Add Maintenance Log</span>`;
                document.getElementById('logId').value = '';
            }
            translateAll();
            showScreen('logForm');
        }

        // --- CRUD Operations ---
        document.getElementById('machineForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const machineData = Object.fromEntries(formData.entries());
            const id = machineData.id;
            delete machineData.id;

            try {
                if (id) {
                    await updateDoc(doc(db, `artifacts/${appId}/public/data/machines`, id), machineData);
                } else {
                    await addDoc(collection(db, `artifacts/${appId}/public/data/machines`), machineData);
                }
                showScreen('home');
            } catch (error) {
                console.error("Error saving machine:", error);
            }
        });
        
        document.getElementById('logForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const machineId = document.getElementById('logMachineId').value;
            if (!machineId) return;

            const logData = {
                date: document.getElementById('log_date').value,
                technician: document.getElementById('log_technician').value,
                description: document.getElementById('log_description').value,
                createdAt: serverTimestamp()
            };
            
            try {
                const logsCollection = collection(db, `artifacts/${appId}/public/data/machines/${machineId}/logs`);
                await addDoc(logsCollection, logData);
                showMachineDetails(machineId);
            } catch (error) {
                console.error("Error adding log:", error);
            }
        });

        function openDeleteConfirm(id) {
            machineToDeleteId = id;
            deleteConfirmModalEl.classList.remove('hidden');
        }

        document.getElementById('confirmDeleteBtn').addEventListener('click', async () => {
            if (!machineToDeleteId) return;
            try {
                // In a real app, a cloud function should delete subcollections.
                await deleteDoc(doc(db, `artifacts/${appId}/public/data/machines`, machineToDeleteId));
                machineToDeleteId = null;
                deleteConfirmModalEl.classList.add('hidden');
                showScreen('home');
            } catch (error) {
                console.error("Error deleting machine:", error);
            }
        });

        // --- Event Listeners ---
        document.getElementById('homeAddMachineBtn').addEventListener('click', () => showMachineForm());
        document.getElementById('addFirstMachineBtn').addEventListener('click', () => showMachineForm());
        document.querySelectorAll('.go-back-btn').forEach(btn => btn.addEventListener('click', () => showScreen('home')));
        document.querySelectorAll('.go-back-log-btn').forEach(btn => btn.addEventListener('click', () => showScreen('details')));
        
        document.getElementById('cancelDeleteBtn').addEventListener('click', () => deleteConfirmModalEl.classList.add('hidden'));
        searchEl.addEventListener('input', filterAndRenderMachines);
        filterEl.addEventListener('change', filterAndRenderMachines);

        // --- Language Switcher ---
        function translateAll() {
            const isArabic = currentLanguage === 'ar';
            document.documentElement.setAttribute('dir', isArabic ? 'rtl' : 'ltr');
            document.documentElement.setAttribute('lang', currentLanguage);

            document.querySelectorAll('[data-en]').forEach(el => {
                const key = `data-${currentLanguage}`;
                if (el.hasAttribute(key)) el.textContent = el.getAttribute(key);
            });
            document.querySelectorAll('[data-placeholder-en]').forEach(el => {
                const key = `data-placeholder-${currentLanguage}`;
                 if (el.hasAttribute(key)) el.placeholder = el.getAttribute(key);
            });
        }

        document.getElementById('langSwitch').addEventListener('click', () => {
            currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
            filterAndRenderMachines(); // Rerender cards with new language
            if(currentScreen === 'details' && currentMachineId) {
                showMachineDetails(currentMachineId); // Rerender details if visible
            }
            translateAll();
        });
        
        // --- Start the app ---
        main();

    </script>
</body>
</html>
