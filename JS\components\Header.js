// DialysisCare - Header Component
// Main navigation header for the application

import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTheme } from './ThemeProvider.js';

const Header = () => {
    const location = useLocation();
    const { toggleTheme, currentTheme, getThemeIcon } = useTheme();
    const [showMobileMenu, setShowMobileMenu] = useState(false);

    const isActive = (path) => {
        return location.pathname === path;
    };

    const DropletIcon = () => (
        <svg 
            className="w-8 h-8 text-white" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
        >
            <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-4-6.5c-.5 2.5-2 4.9-4 6.5C6 11.1 5 13 5 15a7 7 0 0 0 7 7z"
            />
        </svg>
    );

    const SettingsIcon = () => (
        <svg 
            className="w-5 h-5" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
        >
            <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
        </svg>
    );

    const MenuIcon = () => (
        <svg 
            className="w-6 h-6" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
        >
            <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M4 6h16M4 12h16M4 18h16"
            />
        </svg>
    );

    const CloseIcon = () => (
        <svg 
            className="w-6 h-6" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
        >
            <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M6 18L18 6M6 6l12 12"
            />
        </svg>
    );

    return (
        <header className="header">
            <div className="header-container">
                {/* Logo and Title */}
                <Link to="/" className="header-logo">
                    <DropletIcon />
                    <div>
                        <h1 className="text-xl font-bold tracking-tight">DialysisCare</h1>
                        <p className="text-xs text-medical-blue-100 hidden sm:block">
                            Hemodialysis Maintenance Tracker
                        </p>
                    </div>
                </Link>

                {/* Desktop Navigation */}
                <nav className="hidden md:flex items-center space-x-4">
                    <Link
                        to="/"
                        className={`header-nav-link ${
                            isActive('/') ? 'bg-medical-blue-600 text-white' : ''
                        }`}
                    >
                        Dashboard
                    </Link>
                    
                    <button
                        onClick={toggleTheme}
                        className="header-nav-link"
                        title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} theme`}
                    >
                        {getThemeIcon(currentTheme === 'light' ? 'dark' : 'light')}
                    </button>

                    <Link
                        to="/settings"
                        className={`header-nav-link ${
                            isActive('/settings') ? 'bg-medical-blue-600 text-white' : ''
                        }`}
                        title="Settings"
                    >
                        <SettingsIcon />
                    </Link>
                </nav>

                {/* Mobile Menu Button */}
                <button
                    onClick={() => setShowMobileMenu(!showMobileMenu)}
                    className="md:hidden header-nav-link"
                    aria-label="Toggle mobile menu"
                >
                    {showMobileMenu ? <CloseIcon /> : <MenuIcon />}
                </button>
            </div>

            {/* Mobile Navigation */}
            {showMobileMenu && (
                <div className="md:hidden bg-medical-blue-800 border-t border-medical-blue-600">
                    <div className="px-4 py-2 space-y-1">
                        <Link
                            to="/"
                            onClick={() => setShowMobileMenu(false)}
                            className={`
                                block px-3 py-2 rounded-md text-base font-medium transition-colors
                                ${isActive('/') 
                                    ? 'bg-medical-blue-600 text-white' 
                                    : 'text-medical-blue-100 hover:bg-medical-blue-600 hover:text-white'
                                }
                            `}
                        >
                            Dashboard
                        </Link>

                        <Link
                            to="/settings"
                            onClick={() => setShowMobileMenu(false)}
                            className={`
                                block px-3 py-2 rounded-md text-base font-medium transition-colors
                                ${isActive('/settings') 
                                    ? 'bg-medical-blue-600 text-white' 
                                    : 'text-medical-blue-100 hover:bg-medical-blue-600 hover:text-white'
                                }
                            `}
                        >
                            Settings
                        </Link>

                        <button
                            onClick={() => {
                                toggleTheme();
                                setShowMobileMenu(false);
                            }}
                            className="
                                w-full text-left px-3 py-2 rounded-md text-base font-medium
                                text-medical-blue-100 hover:bg-medical-blue-600 hover:text-white
                                transition-colors flex items-center space-x-2
                            "
                        >
                            {getThemeIcon(currentTheme === 'light' ? 'dark' : 'light')}
                            <span>
                                Switch to {currentTheme === 'light' ? 'Dark' : 'Light'} Theme
                            </span>
                        </button>
                    </div>
                </div>
            )}
        </header>
    );
};

export default Header;
