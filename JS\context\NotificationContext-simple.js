// DialysisCare - Simple Notification Context for Testing
// Minimal notification provider for debugging

import React, { createContext, useContext } from 'react';

// Create context
const NotificationContext = createContext();

// Simple provider component
export const NotificationProvider = ({ children }) => {
    console.log('NotificationProvider rendering...');
    
    const value = {
        notifications: [],
        addNotification: () => {},
        removeNotification: () => {}
    };

    return (
        <NotificationContext.Provider value={value}>
            {children}
        </NotificationContext.Provider>
    );
};

// Hook to use the context
export const useNotification = () => {
    const context = useContext(NotificationContext);
    if (!context) {
        throw new Error('useNotification must be used within a NotificationProvider');
    }
    return context;
};

export default NotificationContext;
