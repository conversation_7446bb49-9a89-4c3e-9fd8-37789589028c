import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { MACHINES } from '../constants';

const ChevronLeftIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2"><path d="m15 18-6-6 6-6"></path></svg>
);

const EditIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path><path d="m15 5 4 4"></path></svg>
);

const BookIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path></svg>
);


const MachineDetailsScreen: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const machine = MACHINES.find((m) => m.id === id);

  if (!machine) {
    return (
      <div className="text-center py-10">
        <h2 className="text-xl font-semibold">Machine not found</h2>
        <Link to="/" className="text-brand-blue-600 hover:underline mt-4 inline-block">Go back to Home</Link>
      </div>
    );
  }

  const handleEdit = () => {
    alert(`Edit functionality for machine ${machine.id} would be implemented here.`);
  };

  const formattedDate = machine.lastMaintenanceDate
    ? machine.lastMaintenanceDate.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' })
    : 'Not yet performed';

  return (
    <div>
       <button onClick={() => navigate(-1)} className="inline-flex items-center mb-6 text-sm font-medium text-gray-600 hover:text-brand-blue-700">
          <ChevronLeftIcon />
          Back to list
      </button>

      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="p-6">
          <h2 className="text-3xl font-bold text-gray-900">{machine.model}</h2>
          <p className="text-md text-gray-500 mt-1">Serial Number: {machine.serialNumber}</p>
          
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-sm">
            <div className="border-t border-gray-200 pt-4">
              <dt className="font-medium text-gray-900">Location</dt>
              <dd className="mt-1 text-gray-600">{machine.location}</dd>
            </div>
            <div className="border-t border-gray-200 pt-4">
              <dt className="font-medium text-gray-900">Last Maintenance</dt>
              <dd className="mt-1 text-gray-600">{formattedDate}</dd>
            </div>
             {machine.notes && (
                <div className="md:col-span-2 border-t border-gray-200 pt-4">
                  <dt className="font-medium text-gray-900">Notes</dt>
                  <dd className="mt-1 text-gray-600 whitespace-pre-wrap">{machine.notes}</dd>
                </div>
              )}
          </div>
        </div>
        <div className="bg-gray-50 p-4 border-t border-gray-200 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-end">
          <button
            onClick={handleEdit}
            className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue-500"
          >
            <EditIcon />
            Edit
          </button>
          <Link
            to={`/machine/${machine.id}/log`}
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-brand-blue-600 hover:bg-brand-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue-500"
          >
            <BookIcon />
            Maintenance Log
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MachineDetailsScreen;