<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Professional hemodialysis maintenance tracking system for healthcare facilities. Monitor equipment status, schedule maintenance, and maintain compliance records." />
    <meta name="keywords" content="hemodialysis, maintenance, tracker, healthcare, medical equipment, dialysis machines" />
    <meta name="author" content="Hemodialysis Care Solutions" />

    <title>DialysisCare - Professional Hemodialysis Maintenance Tracker</title>

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="/assets/icons/favicon.svg" />
    <link rel="icon" type="image/png" href="/assets/icons/favicon.png" />
    <link rel="apple-touch-icon" href="/assets/icons/apple-touch-icon.png" />

    <!-- Preload Critical Resources -->
    <link rel="preload" href="./CSS/main.css" as="style" />
    <link rel="preload" href="./CSS/components.css" as="style" />

    <!-- Stylesheets -->
    <link rel="stylesheet" href="./CSS/main.css" />
    <link rel="stylesheet" href="./CSS/components.css" />
    <link rel="stylesheet" href="./CSS/themes.css" />

    <!-- Tailwind CSS for rapid styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'medical-blue': {
                '50': '#eff6ff',
                '100': '#dbeafe',
                '200': '#bfdbfe',
                '300': '#93c5fd',
                '400': '#60a5fa',
                '500': '#3b82f6',
                '600': '#2563eb',
                '700': '#1d4ed8',
                '800': '#1e40af',
                '900': '#1e3a8a',
                '950': '#172554'
              },
              'medical-green': {
                '50': '#f0fdf4',
                '100': '#dcfce7',
                '200': '#bbf7d0',
                '300': '#86efac',
                '400': '#4ade80',
                '500': '#22c55e',
                '600': '#16a34a',
                '700': '#15803d',
                '800': '#166534',
                '900': '#14532d'
              },
              'medical-red': {
                '50': '#fef2f2',
                '100': '#fee2e2',
                '200': '#fecaca',
                '300': '#fca5a5',
                '400': '#f87171',
                '500': '#ef4444',
                '600': '#dc2626',
                '700': '#b91c1c',
                '800': '#991b1b',
                '900': '#7f1d1d'
              }
            },
            fontFamily: {
              'medical': ['Inter', 'system-ui', 'sans-serif'],
              'mono': ['JetBrains Mono', 'monospace']
            }
          }
        }
      }
    </script>

    <!-- React and Dependencies -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script type="importmap">
    {
      "imports": {
        "react": "https://esm.sh/react@18.2.0",
        "react/jsx-runtime": "https://esm.sh/react@18.2.0/jsx-runtime",
        "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
        "react-router-dom": "https://esm.sh/react-router-dom@6.23.0",
        "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
        "react/": "https://esm.sh/react@^19.1.0/"
      }
    }
    </script>
</head>
<body class="bg-gray-50 font-medical antialiased">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-medical-blue-700 flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <h2 class="text-xl font-semibold">Loading DialysisCare...</h2>
            <p class="text-medical-blue-200 mt-2">Initializing maintenance tracking system</p>
        </div>
    </div>

    <!-- Main Application Container -->
    <div id="root" class="min-h-screen"></div>

    <!-- Offline Indicator -->
    <div id="offline-indicator" class="fixed bottom-4 left-4 bg-medical-red-600 text-white px-4 py-2 rounded-lg shadow-lg hidden">
        <span class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            You're offline. Some features may be limited.
        </span>
    </div>

    <!-- Application Scripts -->
    <script type="text/babel" data-type="module" src="./JS/index.js"></script>
    <script type="module" src="./JS/app.js"></script>
    <script src="./JS/utils/offline-handler.js"></script>

    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./JS/sw.js')
                    .then(registration => console.log('SW registered'))
                    .catch(registrationError => console.log('SW registration failed'));
            });
        }

        // Hide loading screen when app loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
            }, 1000);
        });
    </script>
</body>
</html>