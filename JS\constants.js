// DialysisCare - Constants and Sample Data
// Sample data for hemodialysis machines and maintenance logs

export const MACHINES = [
    {
        id: '1',
        name: 'Fresenius 5008',
        model: '5008',
        manufacturer: 'Fresenius Medical Care',
        serialNumber: 'FMC-5008-001',
        location: 'Room 1',
        status: 'operational',
        lastMaintenanceDate: new Date('2024-06-15'),
        nextMaintenanceDate: new Date('2024-07-15'),
        maintenanceLogs: [
            {
                id: '1',
                date: new Date('2024-06-15'),
                technician: '<PERSON>',
                description: 'Routine maintenance - replaced filters, calibrated sensors',
                duration: 2.5,
                partsUsed: 'Filter A, Sensor B',
                cost: 150.00,
                priority: 'medium'
            },
            {
                id: '2',
                date: new Date('2024-05-15'),
                technician: '<PERSON>',
                description: 'Emergency repair - fixed water leak in dialysate circuit',
                duration: 4.0,
                partsUsed: 'Seal Kit, Tubing',
                cost: 275.00,
                priority: 'high'
            }
        ]
    },
    {
        id: '2',
        name: 'Gambro AK 96',
        model: 'AK 96',
        manufacturer: 'Gambro',
        serialNumber: 'GMB-AK96-002',
        location: 'Room 2',
        status: 'maintenance',
        lastMaintenanceDate: new Date('2024-06-20'),
        nextMaintenanceDate: new Date('2024-07-20'),
        maintenanceLogs: [
            {
                id: '3',
                date: new Date('2024-06-20'),
                technician: 'Mike Wilson',
                description: 'Scheduled maintenance - software update and hardware check',
                duration: 3.0,
                partsUsed: 'None',
                cost: 100.00,
                priority: 'low'
            }
        ]
    },
    {
        id: '3',
        name: 'Baxter Artis',
        model: 'Artis',
        manufacturer: 'Baxter',
        serialNumber: 'BXT-ART-003',
        location: 'Room 3',
        status: 'offline',
        lastMaintenanceDate: new Date('2024-05-30'),
        nextMaintenanceDate: new Date('2024-06-30'),
        maintenanceLogs: [
            {
                id: '4',
                date: new Date('2024-05-30'),
                technician: 'Lisa Chen',
                description: 'Critical repair - replaced main pump motor',
                duration: 6.0,
                partsUsed: 'Pump Motor, Gaskets',
                cost: 850.00,
                priority: 'critical'
            }
        ]
    },
    {
        id: '4',
        name: 'Nipro Surdial X',
        model: 'Surdial X',
        manufacturer: 'Nipro',
        serialNumber: 'NPR-SX-004',
        location: 'Mobile Unit A',
        status: 'operational',
        lastMaintenanceDate: new Date('2024-06-25'),
        nextMaintenanceDate: new Date('2024-07-25'),
        maintenanceLogs: [
            {
                id: '5',
                date: new Date('2024-06-25'),
                technician: 'David Brown',
                description: 'Preventive maintenance - cleaned and lubricated components',
                duration: 1.5,
                partsUsed: 'Lubricant, Cleaning Kit',
                cost: 75.00,
                priority: 'low'
            }
        ]
    }
];

export const MACHINE_STATUSES = {
    operational: {
        label: 'Operational',
        color: 'green',
        icon: 'check-circle'
    },
    maintenance: {
        label: 'Under Maintenance',
        color: 'yellow',
        icon: 'wrench'
    },
    offline: {
        label: 'Offline',
        color: 'red',
        icon: 'x-circle'
    },
    unknown: {
        label: 'Unknown',
        color: 'gray',
        icon: 'question-mark-circle'
    }
};

export const MAINTENANCE_PRIORITIES = {
    low: {
        label: 'Low',
        color: 'blue',
        urgency: 1
    },
    medium: {
        label: 'Medium',
        color: 'yellow',
        urgency: 2
    },
    high: {
        label: 'High',
        color: 'orange',
        urgency: 3
    },
    critical: {
        label: 'Critical',
        color: 'red',
        urgency: 4
    }
};

export const MANUFACTURERS = [
    'Fresenius Medical Care',
    'Gambro',
    'Baxter',
    'Nipro',
    'B. Braun',
    'Nikkiso',
    'Toray Medical'
];

export const COMMON_PARTS = [
    'Filter A',
    'Filter B',
    'Sensor B',
    'Pump Motor',
    'Gaskets',
    'Seal Kit',
    'Tubing',
    'Lubricant',
    'Cleaning Kit',
    'Pressure Sensor',
    'Flow Meter',
    'Temperature Sensor',
    'pH Sensor',
    'Conductivity Sensor'
];

export const TECHNICIANS = [
    'John Smith',
    'Sarah Johnson',
    'Mike Wilson',
    'Lisa Chen',
    'David Brown',
    'Emily Davis',
    'Robert Taylor',
    'Jennifer Martinez'
];

export const LOCATIONS = [
    'Room 1',
    'Room 2',
    'Room 3',
    'Room 4',
    'Room 5',
    'Mobile Unit A',
    'Mobile Unit B',
    'Storage',
    'Maintenance Bay'
];

// Utility functions
export const getStatusColor = (status) => {
    return MACHINE_STATUSES[status]?.color || 'gray';
};

export const getPriorityColor = (priority) => {
    return MAINTENANCE_PRIORITIES[priority]?.color || 'gray';
};

export const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
};

export const formatCurrency = (amount) => {
    if (!amount) return '$0.00';
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

export const getDaysUntilMaintenance = (nextMaintenanceDate) => {
    if (!nextMaintenanceDate) return null;
    const today = new Date();
    const nextDate = new Date(nextMaintenanceDate);
    const diffTime = nextDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
};

export const getMaintenanceStatus = (nextMaintenanceDate) => {
    const days = getDaysUntilMaintenance(nextMaintenanceDate);
    if (days === null) return 'unknown';
    if (days < 0) return 'overdue';
    if (days <= 7) return 'due-soon';
    if (days <= 30) return 'upcoming';
    return 'scheduled';
};
