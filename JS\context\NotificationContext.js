// DialysisCare - Notification Context
// Provides notification system for the application

import React, { createContext, useContext, useState, useCallback } from 'react';

// Notification types
export const NotificationTypes = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
};

// Create context
const NotificationContext = createContext();

// Notification provider component
export const NotificationProvider = ({ children }) => {
    const [notifications, setNotifications] = useState([]);

    // Add notification
    const addNotification = useCallback((notification) => {
        const id = Date.now().toString();
        const newNotification = {
            id,
            type: NotificationTypes.INFO,
            duration: 5000,
            ...notification,
            timestamp: new Date()
        };

        setNotifications(prev => [...prev, newNotification]);

        // Auto-remove notification after duration
        if (newNotification.duration > 0) {
            setTimeout(() => {
                removeNotification(id);
            }, newNotification.duration);
        }

        return id;
    }, []);

    // Remove notification
    const removeNotification = useCallback((id) => {
        setNotifications(prev => prev.filter(notification => notification.id !== id));
    }, []);

    // Clear all notifications
    const clearNotifications = useCallback(() => {
        setNotifications([]);
    }, []);

    // Convenience methods for different notification types
    const showSuccess = useCallback((message, options = {}) => {
        return addNotification({
            type: NotificationTypes.SUCCESS,
            message,
            ...options
        });
    }, [addNotification]);

    const showError = useCallback((message, options = {}) => {
        return addNotification({
            type: NotificationTypes.ERROR,
            message,
            duration: 8000, // Errors stay longer
            ...options
        });
    }, [addNotification]);

    const showWarning = useCallback((message, options = {}) => {
        return addNotification({
            type: NotificationTypes.WARNING,
            message,
            duration: 6000,
            ...options
        });
    }, [addNotification]);

    const showInfo = useCallback((message, options = {}) => {
        return addNotification({
            type: NotificationTypes.INFO,
            message,
            ...options
        });
    }, [addNotification]);

    const value = {
        notifications,
        addNotification,
        removeNotification,
        clearNotifications,
        showSuccess,
        showError,
        showWarning,
        showInfo
    };

    return (
        <NotificationContext.Provider value={value}>
            {children}
            <NotificationContainer />
        </NotificationContext.Provider>
    );
};

// Notification container component
const NotificationContainer = () => {
    const { notifications, removeNotification } = useNotification();

    if (notifications.length === 0) return null;

    return (
        <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
            {notifications.map(notification => (
                <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onRemove={() => removeNotification(notification.id)}
                />
            ))}
        </div>
    );
};

// Individual notification item component
const NotificationItem = ({ notification, onRemove }) => {
    const getNotificationStyles = (type) => {
        const baseStyles = "p-4 rounded-lg shadow-lg border-l-4 flex items-start space-x-3 max-w-sm animate-slide-in";
        
        switch (type) {
            case NotificationTypes.SUCCESS:
                return `${baseStyles} bg-green-50 border-green-400 text-green-800`;
            case NotificationTypes.ERROR:
                return `${baseStyles} bg-red-50 border-red-400 text-red-800`;
            case NotificationTypes.WARNING:
                return `${baseStyles} bg-yellow-50 border-yellow-400 text-yellow-800`;
            case NotificationTypes.INFO:
            default:
                return `${baseStyles} bg-blue-50 border-blue-400 text-blue-800`;
        }
    };

    const getIcon = (type) => {
        const iconClass = "w-5 h-5 mt-0.5 flex-shrink-0";
        
        switch (type) {
            case NotificationTypes.SUCCESS:
                return (
                    <svg className={`${iconClass} text-green-400`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                );
            case NotificationTypes.ERROR:
                return (
                    <svg className={`${iconClass} text-red-400`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                );
            case NotificationTypes.WARNING:
                return (
                    <svg className={`${iconClass} text-yellow-400`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                );
            case NotificationTypes.INFO:
            default:
                return (
                    <svg className={`${iconClass} text-blue-400`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                );
        }
    };

    return (
        <div className={getNotificationStyles(notification.type)}>
            {getIcon(notification.type)}
            <div className="flex-1 min-w-0">
                {notification.title && (
                    <h4 className="font-medium text-sm mb-1">{notification.title}</h4>
                )}
                <p className="text-sm">{notification.message}</p>
                {notification.action && (
                    <button
                        onClick={notification.action.onClick}
                        className="mt-2 text-sm font-medium underline hover:no-underline"
                    >
                        {notification.action.label}
                    </button>
                )}
            </div>
            <button
                onClick={onRemove}
                className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Close notification"
            >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    );
};

// Custom hook to use notifications
export const useNotification = () => {
    const context = useContext(NotificationContext);
    if (!context) {
        throw new Error('useNotification must be used within a NotificationProvider');
    }
    return context;
};

export default NotificationContext;
