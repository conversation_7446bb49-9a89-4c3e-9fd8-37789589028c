/* DialysisCare - Component Specific Styles */
/* Hemodialysis Maintenance Tracker Components */

/* Header Component */
.header {
    background: linear-gradient(135deg, var(--medical-blue-700) 0%, var(--medical-blue-800) 100%);
    color: white;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.header-logo:hover {
    color: var(--medical-blue-100);
    text-decoration: none;
}

.header-logo svg {
    width: 2rem;
    height: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.header-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-nav-link {
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    color: var(--medical-blue-100);
    transition: all var(--transition-fast);
}

.header-nav-link:hover {
    background-color: var(--medical-blue-600);
    color: white;
    text-decoration: none;
}

/* Machine Card Component */
.machine-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.machine-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.machine-card-header {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 1px solid var(--gray-200);
}

.machine-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.machine-card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
}

.machine-card-body {
    padding: var(--spacing-lg);
}

.machine-card-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.machine-card-info-item {
    display: flex;
    flex-direction: column;
}

.machine-card-info-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-xs);
}

.machine-card-info-value {
    font-size: 0.875rem;
    color: var(--gray-900);
    font-weight: 500;
}

.machine-card-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.machine-card-status.status-good {
    background-color: var(--medical-green-100);
    color: var(--medical-green-800);
}

.machine-card-status.status-warning {
    background-color: var(--medical-orange-100);
    color: var(--medical-orange-800);
}

.machine-card-status.status-critical {
    background-color: var(--medical-red-100);
    color: var(--medical-red-800);
}

.machine-card-status.status-unknown {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.machine-card-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.machine-card-notes {
    background-color: var(--gray-50);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-lg);
    border-left: 4px solid var(--medical-blue-500);
}

.machine-card-notes-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.machine-card-notes-content {
    font-size: 0.875rem;
    color: var(--gray-700);
    line-height: 1.5;
    margin: 0;
}

/* Maintenance Log Entry Component */
.log-entry-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: box-shadow var(--transition-fast);
}

.log-entry-card:hover {
    box-shadow: var(--shadow-md);
}

.log-entry-card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.log-entry-card-date {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--medical-blue-700);
}

.log-entry-card-technician {
    font-size: 0.75rem;
    color: var(--gray-600);
    background-color: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.log-entry-card-body {
    padding: var(--spacing-lg);
}

.log-entry-card-description {
    font-size: 0.875rem;
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.status-operational .status-indicator-dot {
    background-color: var(--medical-green-500);
}

.status-indicator.status-maintenance .status-indicator-dot {
    background-color: var(--medical-orange-500);
}

.status-indicator.status-offline .status-indicator-dot {
    background-color: var(--medical-red-500);
}

.status-indicator.status-unknown .status-indicator-dot {
    background-color: var(--gray-400);
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--medical-blue-600) 0%, var(--medical-blue-700) 100%);
    color: white;
    border: none;
    box-shadow: var(--shadow-xl);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 20px 25px -5px rgb(37 99 235 / 0.3), 0 8px 10px -6px rgb(37 99 235 / 0.3);
}

.fab:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.3);
}

.fab svg {
    width: 1.5rem;
    height: 1.5rem;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--medical-blue-600);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Alert Components */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.alert-success {
    background-color: var(--medical-green-50);
    border-color: var(--medical-green-200);
    color: var(--medical-green-800);
}

.alert-warning {
    background-color: var(--medical-orange-50);
    border-color: var(--medical-orange-200);
    color: var(--medical-orange-800);
}

.alert-error {
    background-color: var(--medical-red-50);
    border-color: var(--medical-red-200);
    color: var(--medical-red-800);
}

.alert-info {
    background-color: var(--medical-blue-50);
    border-color: var(--medical-blue-200);
    color: var(--medical-blue-800);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        padding: 0 var(--spacing-md);
    }
    
    .header-logo {
        font-size: 1rem;
    }
    
    .machine-card-info {
        grid-template-columns: 1fr;
    }
    
    .machine-card-actions {
        flex-direction: column;
    }
    
    .fab {
        bottom: 1rem;
        right: 1rem;
        width: 3rem;
        height: 3rem;
    }
    
    .fab svg {
        width: 1.25rem;
        height: 1.25rem;
    }
}

@media (max-width: 480px) {
    .machine-card-header,
    .machine-card-body {
        padding: var(--spacing-md);
    }
    
    .log-entry-card-header,
    .log-entry-card-body {
        padding: var(--spacing-md);
    }
}
