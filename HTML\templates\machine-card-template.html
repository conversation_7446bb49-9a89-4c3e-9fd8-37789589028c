<!-- DialysisCare - Machine Card Template -->
<!-- Template for displaying hemodialysis machine information -->

<template id="machine-card-template">
    <div class="machine-card fade-in">
        <!-- Machine Card Header -->
        <div class="machine-card-header">
            <div class="flex justify-between items-start">
                <div>
                    <h3 class="machine-card-title" data-field="model">Machine Model</h3>
                    <p class="machine-card-subtitle">
                        Serial: <span data-field="serialNumber">Serial Number</span>
                    </p>
                </div>
                <div class="machine-card-status status-unknown" data-field="status">
                    <span class="status-indicator-dot"></span>
                    <span data-field="statusText">Unknown</span>
                </div>
            </div>
        </div>

        <!-- Machine Card Body -->
        <div class="machine-card-body">
            <!-- Machine Information Grid -->
            <div class="machine-card-info">
                <div class="machine-card-info-item">
                    <span class="machine-card-info-label">Location</span>
                    <span class="machine-card-info-value" data-field="location">Location</span>
                </div>
                
                <div class="machine-card-info-item">
                    <span class="machine-card-info-label">Last Maintenance</span>
                    <span class="machine-card-info-value" data-field="lastMaintenance">Never</span>
                </div>
                
                <div class="machine-card-info-item">
                    <span class="machine-card-info-label">Next Due</span>
                    <span class="machine-card-info-value" data-field="nextDue">Not Scheduled</span>
                </div>
                
                <div class="machine-card-info-item">
                    <span class="machine-card-info-label">Total Logs</span>
                    <span class="machine-card-info-value" data-field="totalLogs">0</span>
                </div>
            </div>

            <!-- Machine Notes (if available) -->
            <div class="machine-card-notes" data-field="notes" style="display: none;">
                <h4 class="machine-card-notes-title">Notes</h4>
                <p class="machine-card-notes-content" data-field="notesContent"></p>
            </div>

            <!-- Action Buttons -->
            <div class="machine-card-actions">
                <a href="#" class="btn btn-primary" data-action="view-details">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View Details
                </a>
                
                <a href="#" class="btn btn-secondary" data-action="view-logs">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Maintenance Log
                </a>
                
                <button class="btn btn-success" data-action="add-maintenance">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Maintenance
                </button>
            </div>
        </div>
    </div>
</template>

<!-- Loading Skeleton Template -->
<template id="machine-card-skeleton-template">
    <div class="machine-card">
        <div class="machine-card-header">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <div class="loading-skeleton h-6 w-3/4 mb-2 rounded"></div>
                    <div class="loading-skeleton h-4 w-1/2 rounded"></div>
                </div>
                <div class="loading-skeleton h-6 w-20 rounded-full"></div>
            </div>
        </div>
        
        <div class="machine-card-body">
            <div class="machine-card-info">
                <div class="machine-card-info-item">
                    <div class="loading-skeleton h-3 w-16 mb-1 rounded"></div>
                    <div class="loading-skeleton h-4 w-24 rounded"></div>
                </div>
                <div class="machine-card-info-item">
                    <div class="loading-skeleton h-3 w-20 mb-1 rounded"></div>
                    <div class="loading-skeleton h-4 w-20 rounded"></div>
                </div>
                <div class="machine-card-info-item">
                    <div class="loading-skeleton h-3 w-16 mb-1 rounded"></div>
                    <div class="loading-skeleton h-4 w-24 rounded"></div>
                </div>
                <div class="machine-card-info-item">
                    <div class="loading-skeleton h-3 w-18 mb-1 rounded"></div>
                    <div class="loading-skeleton h-4 w-8 rounded"></div>
                </div>
            </div>
            
            <div class="machine-card-actions">
                <div class="loading-skeleton h-10 w-28 rounded"></div>
                <div class="loading-skeleton h-10 w-32 rounded"></div>
                <div class="loading-skeleton h-10 w-36 rounded"></div>
            </div>
        </div>
    </div>
</template>

<!-- Empty State Template -->
<template id="machines-empty-state-template">
    <div class="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg bg-white">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Machines Found</h3>
        <p class="text-sm text-gray-500 mb-6">
            Get started by adding your first hemodialysis machine to the system.
        </p>
        <button class="btn btn-primary" data-action="add-machine">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add First Machine
        </button>
    </div>
</template>

<!-- Error State Template -->
<template id="machines-error-state-template">
    <div class="text-center py-12 bg-white border border-red-200 rounded-lg">
        <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Machines</h3>
        <p class="text-sm text-gray-500 mb-6" data-field="errorMessage">
            There was a problem loading the machine data. Please try again.
        </p>
        <button class="btn btn-primary" data-action="retry-load">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Try Again
        </button>
    </div>
</template>

<!-- Maintenance Status Indicators -->
<template id="status-indicators-template">
    <!-- Operational Status -->
    <div class="status-indicator status-operational" data-status="operational">
        <span class="status-indicator-dot"></span>
        <span>Operational</span>
    </div>
    
    <!-- Maintenance Required -->
    <div class="status-indicator status-maintenance" data-status="maintenance">
        <span class="status-indicator-dot"></span>
        <span>Maintenance Due</span>
    </div>
    
    <!-- Offline/Critical -->
    <div class="status-indicator status-offline" data-status="offline">
        <span class="status-indicator-dot"></span>
        <span>Offline</span>
    </div>
    
    <!-- Unknown Status -->
    <div class="status-indicator status-unknown" data-status="unknown">
        <span class="status-indicator-dot"></span>
        <span>Unknown</span>
    </div>
</template>

<script>
// Template utility functions
window.DialysisCareTemplates = {
    // Clone and populate machine card template
    createMachineCard: function(machineData) {
        const template = document.getElementById('machine-card-template');
        const clone = template.content.cloneNode(true);
        
        // Populate data fields
        const fields = clone.querySelectorAll('[data-field]');
        fields.forEach(field => {
            const fieldName = field.getAttribute('data-field');
            if (machineData[fieldName] !== undefined) {
                if (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA') {
                    field.value = machineData[fieldName];
                } else {
                    field.textContent = machineData[fieldName];
                }
            }
        });
        
        // Set up action handlers
        const actions = clone.querySelectorAll('[data-action]');
        actions.forEach(action => {
            const actionName = action.getAttribute('data-action');
            action.addEventListener('click', (e) => {
                e.preventDefault();
                window.dispatchEvent(new CustomEvent('machine-action', {
                    detail: { action: actionName, machineId: machineData.id }
                }));
            });
        });
        
        return clone;
    },
    
    // Create loading skeleton
    createLoadingSkeleton: function() {
        const template = document.getElementById('machine-card-skeleton-template');
        return template.content.cloneNode(true);
    },
    
    // Create empty state
    createEmptyState: function() {
        const template = document.getElementById('machines-empty-state-template');
        return template.content.cloneNode(true);
    },
    
    // Create error state
    createErrorState: function(errorMessage) {
        const template = document.getElementById('machines-error-state-template');
        const clone = template.content.cloneNode(true);
        
        const messageElement = clone.querySelector('[data-field="errorMessage"]');
        if (messageElement && errorMessage) {
            messageElement.textContent = errorMessage;
        }
        
        return clone;
    }
};
</script>
