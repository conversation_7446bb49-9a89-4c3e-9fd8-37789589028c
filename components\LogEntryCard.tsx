import React from 'react';
import { MaintenanceLogEntry } from '../types';

interface LogEntryCardProps {
    log: MaintenanceLogEntry;
}

const UserIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-1.5 text-gray-400"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
);

const LogEntryCard: React.FC<LogEntryCardProps> = ({ log }) => {
    const formattedDate = log.date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });

    return (
        <div className="bg-white p-4 rounded-md shadow-sm border border-gray-200">
            <div className="flex justify-between items-center mb-2">
                <p className="text-sm font-semibold text-brand-blue-800">{formattedDate}</p>
                <div className="flex items-center text-xs text-gray-500">
                   <UserIcon />
                    <span>{log.technician}</span>
                </div>
            </div>
            <p className="text-gray-700">{log.description}</p>
        </div>
    );
};

export default LogEntryCard;
