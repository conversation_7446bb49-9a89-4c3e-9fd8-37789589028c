// DialysisCare - Simple Application Context for Testing
// Minimal context provider for debugging

import React, { createContext, useContext } from 'react';

// Create context
const AppContext = createContext();

// Simple provider component
export const AppProvider = ({ children }) => {
    console.log('AppProvider rendering...');
    
    const value = {
        machines: [],
        loading: false,
        error: null
    };

    return (
        <AppContext.Provider value={value}>
            {children}
        </AppContext.Provider>
    );
};

// Hook to use the context
export const useApp = () => {
    const context = useContext(AppContext);
    if (!context) {
        throw new Error('useApp must be used within an AppProvider');
    }
    return context;
};

export default AppContext;
