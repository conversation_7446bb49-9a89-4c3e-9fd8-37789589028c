/* DialysisCare - Main Stylesheet */
/* Professional Hemodialysis Maintenance Tracker */

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #f9fafb;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Medical Color Variables */
:root {
    /* Primary Medical Blue */
    --medical-blue-50: #eff6ff;
    --medical-blue-100: #dbeafe;
    --medical-blue-200: #bfdbfe;
    --medical-blue-300: #93c5fd;
    --medical-blue-400: #60a5fa;
    --medical-blue-500: #3b82f6;
    --medical-blue-600: #2563eb;
    --medical-blue-700: #1d4ed8;
    --medical-blue-800: #1e40af;
    --medical-blue-900: #1e3a8a;

    /* Medical Green (Success/Healthy) */
    --medical-green-50: #f0fdf4;
    --medical-green-100: #dcfce7;
    --medical-green-200: #bbf7d0;
    --medical-green-300: #86efac;
    --medical-green-400: #4ade80;
    --medical-green-500: #22c55e;
    --medical-green-600: #16a34a;
    --medical-green-700: #15803d;
    --medical-green-800: #166534;
    --medical-green-900: #14532d;

    /* Medical Red (Alert/Critical) */
    --medical-red-50: #fef2f2;
    --medical-red-100: #fee2e2;
    --medical-red-200: #fecaca;
    --medical-red-300: #fca5a5;
    --medical-red-400: #f87171;
    --medical-red-500: #ef4444;
    --medical-red-600: #dc2626;
    --medical-red-700: #b91c1c;
    --medical-red-800: #991b1b;
    --medical-red-900: #7f1d1d;

    /* Medical Orange (Warning) */
    --medical-orange-50: #fff7ed;
    --medical-orange-100: #ffedd5;
    --medical-orange-200: #fed7aa;
    --medical-orange-300: #fdba74;
    --medical-orange-400: #fb923c;
    --medical-orange-500: #f97316;
    --medical-orange-600: #ea580c;
    --medical-orange-700: #c2410c;
    --medical-orange-800: #9a3412;
    --medical-orange-900: #7c2d12;

    /* Neutral Grays */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

h1 {
    font-size: 2.25rem;
    font-weight: 700;
}

h2 {
    font-size: 1.875rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

h5 {
    font-size: 1.125rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: var(--spacing-md);
    color: var(--gray-700);
}

/* Links */
a {
    color: var(--medical-blue-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--medical-blue-700);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--medical-blue-500);
    outline-offset: 2px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--medical-blue-500);
}

.btn-primary {
    background-color: var(--medical-blue-600);
    color: white;
}

.btn-primary:hover {
    background-color: var(--medical-blue-700);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
    text-decoration: none;
}

.btn-success {
    background-color: var(--medical-green-600);
    color: white;
}

.btn-success:hover {
    background-color: var(--medical-green-700);
    color: white;
    text-decoration: none;
}

.btn-danger {
    background-color: var(--medical-red-600);
    color: white;
}

.btn-danger:hover {
    background-color: var(--medical-red-700);
    color: white;
    text-decoration: none;
}

.btn-warning {
    background-color: var(--medical-orange-500);
    color: white;
}

.btn-warning:hover {
    background-color: var(--medical-orange-600);
    color: white;
    text-decoration: none;
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xs);
}

.form-input,
.form-select,
.form-textarea {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background-color: white;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--medical-blue-500);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Cards */
.card {
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden {
    display: none;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Responsive Design */
@media (max-width: 640px) {
    html {
        font-size: 14px;
    }
    
    .card-body,
    .card-header,
    .card-footer {
        padding: var(--spacing-md);
    }
}

/* Print Styles */
@media print {
    body {
        background-color: white;
        color: black;
    }
    
    .btn,
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
