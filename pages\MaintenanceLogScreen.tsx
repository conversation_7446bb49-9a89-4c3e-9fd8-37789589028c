import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { MACHINES } from '../constants';
import LogEntryCard from '../components/LogEntryCard';

const ChevronLeftIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2"><path d="m15 18-6-6 6-6"></path></svg>
);

const PlusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2"><line x1="12" x2="12" y1="5" y2="19"></line><line x1="5" x2="19" y1="12" y2="12"></line></svg>
);

const MaintenanceLogScreen: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const machine = MACHINES.find((m) => m.id === id);

  if (!machine) {
    return (
      <div className="text-center py-10">
        <h2 className="text-xl font-semibold">Machine not found</h2>
        <Link to="/" className="text-brand-blue-600 hover:underline mt-4 inline-block">Go back to Home</Link>
      </div>
    );
  }

  const handleAddLogEntry = () => {
    alert(`Functionality to add a new log entry for machine ${machine.id} would be implemented here.`);
  };

  return (
    <div>
        <button onClick={() => navigate(`/machine/${id}`)} className="inline-flex items-center mb-6 text-sm font-medium text-gray-600 hover:text-brand-blue-700">
          <ChevronLeftIcon />
          Back to Machine Details
      </button>

      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Maintenance Log</h2>
                <p className="text-md text-gray-500 mt-1">
                  For {machine.model} - SN: {machine.serialNumber}
                </p>
              </div>
              <button
                onClick={handleAddLogEntry}
                className="mt-4 sm:mt-0 inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-brand-blue-600 hover:bg-brand-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue-500"
                >
                <PlusIcon />
                Add New Entry
              </button>
          </div>

          <div className="mt-6">
            {machine.maintenanceLogs.length > 0 ? (
              <div className="space-y-4">
                {machine.maintenanceLogs
                  .sort((a, b) => b.date.getTime() - a.date.getTime())
                  .map((log) => (
                    <LogEntryCard key={log.id} log={log} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">No Log Entries</h3>
                <p className="mt-1 text-sm text-gray-500">This machine has no maintenance history yet.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceLogScreen;