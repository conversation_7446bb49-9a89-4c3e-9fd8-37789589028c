// DialysisCare - Settings Screen Component
// Application settings and configuration

import React, { useState } from 'react';
import { useNotification } from '../context/NotificationContext.js';
import { ThemeSelector } from '../components/ThemeProvider.js';

const SettingsScreen = () => {
    const { showSuccess, showError } = useNotification();
    const [activeTab, setActiveTab] = useState('general');
    const [settings, setSettings] = useState({
        // General settings
        autoSave: true,
        notifications: true,
        soundAlerts: false,
        defaultView: 'grid',
        
        // Maintenance settings
        maintenanceInterval: 30,
        reminderDays: 7,
        autoSchedule: false,
        
        // Data settings
        backupEnabled: true,
        exportFormat: 'csv',
        dataRetention: 365,
        
        // User preferences
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        currency: 'USD'
    });

    const handleSettingChange = (key, value) => {
        setSettings(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const saveSettings = () => {
        try {
            localStorage.setItem('dialysiscare-settings', JSON.stringify(settings));
            showSuccess('Settings saved successfully');
        } catch (error) {
            showError('Failed to save settings');
        }
    };

    const resetSettings = () => {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            localStorage.removeItem('dialysiscare-settings');
            setSettings({
                autoSave: true,
                notifications: true,
                soundAlerts: false,
                defaultView: 'grid',
                maintenanceInterval: 30,
                reminderDays: 7,
                autoSchedule: false,
                backupEnabled: true,
                exportFormat: 'csv',
                dataRetention: 365,
                language: 'en',
                timezone: 'UTC',
                dateFormat: 'MM/DD/YYYY',
                currency: 'USD'
            });
            showSuccess('Settings reset to defaults');
        }
    };

    const exportData = () => {
        try {
            const data = localStorage.getItem('dialysiscare-machines');
            if (data) {
                const blob = new Blob([data], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `dialysiscare-backup-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                window.URL.revokeObjectURL(url);
                showSuccess('Data exported successfully');
            } else {
                showError('No data to export');
            }
        } catch (error) {
            showError('Failed to export data');
        }
    };

    const importData = (event) => {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    localStorage.setItem('dialysiscare-machines', JSON.stringify(data));
                    showSuccess('Data imported successfully. Please refresh the page.');
                } catch (error) {
                    showError('Invalid file format');
                }
            };
            reader.readAsText(file);
        }
    };

    const clearAllData = () => {
        if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
            localStorage.removeItem('dialysiscare-machines');
            localStorage.removeItem('dialysiscare-settings');
            showSuccess('All data cleared. Please refresh the page.');
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div>
                <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
                <p className="text-gray-600 mt-1">
                    Configure your DialysisCare application preferences
                </p>
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-lg shadow">
                <div className="border-b border-gray-200">
                    <nav className="flex space-x-8 px-6">
                        {[
                            { id: 'general', label: 'General' },
                            { id: 'maintenance', label: 'Maintenance' },
                            { id: 'appearance', label: 'Appearance' },
                            { id: 'data', label: 'Data & Backup' }
                        ].map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`
                                    py-4 px-1 border-b-2 font-medium text-sm transition-colors
                                    ${activeTab === tab.id
                                        ? 'border-medical-blue-500 text-medical-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }
                                `}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </nav>
                </div>

                <div className="p-6">
                    {activeTab === 'general' && (
                        <GeneralSettings 
                            settings={settings} 
                            onChange={handleSettingChange} 
                        />
                    )}
                    {activeTab === 'maintenance' && (
                        <MaintenanceSettings 
                            settings={settings} 
                            onChange={handleSettingChange} 
                        />
                    )}
                    {activeTab === 'appearance' && (
                        <AppearanceSettings 
                            settings={settings} 
                            onChange={handleSettingChange} 
                        />
                    )}
                    {activeTab === 'data' && (
                        <DataSettings 
                            onExport={exportData}
                            onImport={importData}
                            onClearData={clearAllData}
                            settings={settings}
                            onChange={handleSettingChange}
                        />
                    )}
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
                <button
                    onClick={resetSettings}
                    className="btn-secondary"
                >
                    Reset to Defaults
                </button>
                <button
                    onClick={saveSettings}
                    className="btn-primary"
                >
                    Save Settings
                </button>
            </div>
        </div>
    );
};

// General Settings Tab
const GeneralSettings = ({ settings, onChange }) => {
    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">General Preferences</h3>
                
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">
                                Auto-save changes
                            </label>
                            <p className="text-sm text-gray-500">
                                Automatically save changes as you make them
                            </p>
                        </div>
                        <input
                            type="checkbox"
                            checked={settings.autoSave}
                            onChange={(e) => onChange('autoSave', e.target.checked)}
                            className="toggle-switch"
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">
                                Enable notifications
                            </label>
                            <p className="text-sm text-gray-500">
                                Show system notifications for important events
                            </p>
                        </div>
                        <input
                            type="checkbox"
                            checked={settings.notifications}
                            onChange={(e) => onChange('notifications', e.target.checked)}
                            className="toggle-switch"
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">
                                Sound alerts
                            </label>
                            <p className="text-sm text-gray-500">
                                Play sound for alerts and notifications
                            </p>
                        </div>
                        <input
                            type="checkbox"
                            checked={settings.soundAlerts}
                            onChange={(e) => onChange('soundAlerts', e.target.checked)}
                            className="toggle-switch"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Default view mode
                        </label>
                        <select
                            value={settings.defaultView}
                            onChange={(e) => onChange('defaultView', e.target.value)}
                            className="input-field max-w-xs"
                        >
                            <option value="grid">Grid View</option>
                            <option value="list">List View</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Language
                        </label>
                        <select
                            value={settings.language}
                            onChange={(e) => onChange('language', e.target.value)}
                            className="input-field max-w-xs"
                        >
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Date format
                        </label>
                        <select
                            value={settings.dateFormat}
                            onChange={(e) => onChange('dateFormat', e.target.value)}
                            className="input-field max-w-xs"
                        >
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    );
};

// Maintenance Settings Tab
const MaintenanceSettings = ({ settings, onChange }) => {
    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Maintenance Configuration</h3>
                
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Default maintenance interval (days)
                        </label>
                        <input
                            type="number"
                            min="1"
                            max="365"
                            value={settings.maintenanceInterval}
                            onChange={(e) => onChange('maintenanceInterval', parseInt(e.target.value))}
                            className="input-field max-w-xs"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                            Default interval for scheduling maintenance
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Reminder days before maintenance
                        </label>
                        <input
                            type="number"
                            min="1"
                            max="30"
                            value={settings.reminderDays}
                            onChange={(e) => onChange('reminderDays', parseInt(e.target.value))}
                            className="input-field max-w-xs"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                            Show reminders this many days before maintenance is due
                        </p>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">
                                Auto-schedule maintenance
                            </label>
                            <p className="text-sm text-gray-500">
                                Automatically schedule next maintenance after completion
                            </p>
                        </div>
                        <input
                            type="checkbox"
                            checked={settings.autoSchedule}
                            onChange={(e) => onChange('autoSchedule', e.target.checked)}
                            className="toggle-switch"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

// Appearance Settings Tab
const AppearanceSettings = ({ settings, onChange }) => {
    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Appearance & Theme</h3>
                
                <div className="space-y-6">
                    <ThemeSelector />
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Currency
                        </label>
                        <select
                            value={settings.currency}
                            onChange={(e) => onChange('currency', e.target.value)}
                            className="input-field max-w-xs"
                        >
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="CAD">CAD (C$)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    );
};

// Data Settings Tab
const DataSettings = ({ onExport, onImport, onClearData, settings, onChange }) => {
    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Data Management</h3>
                
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">
                                Enable automatic backups
                            </label>
                            <p className="text-sm text-gray-500">
                                Automatically backup data to browser storage
                            </p>
                        </div>
                        <input
                            type="checkbox"
                            checked={settings.backupEnabled}
                            onChange={(e) => onChange('backupEnabled', e.target.checked)}
                            className="toggle-switch"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Export format
                        </label>
                        <select
                            value={settings.exportFormat}
                            onChange={(e) => onChange('exportFormat', e.target.value)}
                            className="input-field max-w-xs"
                        >
                            <option value="csv">CSV</option>
                            <option value="json">JSON</option>
                            <option value="xlsx">Excel</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Data retention (days)
                        </label>
                        <input
                            type="number"
                            min="30"
                            max="3650"
                            value={settings.dataRetention}
                            onChange={(e) => onChange('dataRetention', parseInt(e.target.value))}
                            className="input-field max-w-xs"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                            How long to keep maintenance logs
                        </p>
                    </div>
                </div>
            </div>

            <div className="border-t pt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Data Operations</h3>
                
                <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                        <div>
                            <h4 className="font-medium text-blue-900">Export Data</h4>
                            <p className="text-sm text-blue-700">
                                Download a backup of all your data
                            </p>
                        </div>
                        <button onClick={onExport} className="btn-primary">
                            Export
                        </button>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                        <div>
                            <h4 className="font-medium text-green-900">Import Data</h4>
                            <p className="text-sm text-green-700">
                                Restore data from a backup file
                            </p>
                        </div>
                        <label className="btn-primary cursor-pointer">
                            Import
                            <input
                                type="file"
                                accept=".json"
                                onChange={onImport}
                                className="hidden"
                            />
                        </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                        <div>
                            <h4 className="font-medium text-red-900">Clear All Data</h4>
                            <p className="text-sm text-red-700">
                                Permanently delete all data and settings
                            </p>
                        </div>
                        <button 
                            onClick={onClearData} 
                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Clear Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SettingsScreen;
