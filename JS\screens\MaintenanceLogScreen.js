// DialysisCare - Maintenance Log Screen Component
// Comprehensive view of all maintenance logs across all machines

import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext.js';
import { 
    formatDate, 
    formatCurrency, 
    MAINTENANCE_PRIORITIES 
} from '../constants.js';

const MaintenanceLogScreen = () => {
    const { machines } = useApp();
    const [filters, setFilters] = useState({
        search: '',
        machine: 'all',
        technician: 'all',
        priority: 'all',
        dateFrom: '',
        dateTo: ''
    });
    const [sortBy, setSortBy] = useState('date');
    const [sortOrder, setSortOrder] = useState('desc');

    // Flatten all maintenance logs with machine info
    const allLogs = useMemo(() => {
        return machines.flatMap(machine =>
            machine.maintenanceLogs.map(log => ({
                ...log,
                machineId: machine.id,
                machineName: machine.name,
                machineLocation: machine.location
            }))
        );
    }, [machines]);

    // Get unique values for filters
    const uniqueTechnicians = [...new Set(allLogs.map(log => log.technician))].filter(Boolean);
    const uniqueMachines = machines.map(m => ({ id: m.id, name: m.name }));

    // Filter logs
    const filteredLogs = useMemo(() => {
        let filtered = allLogs;

        // Search filter
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filtered = filtered.filter(log =>
                log.description.toLowerCase().includes(searchTerm) ||
                log.technician.toLowerCase().includes(searchTerm) ||
                log.machineName.toLowerCase().includes(searchTerm) ||
                log.partsUsed?.toLowerCase().includes(searchTerm)
            );
        }

        // Machine filter
        if (filters.machine !== 'all') {
            filtered = filtered.filter(log => log.machineId === filters.machine);
        }

        // Technician filter
        if (filters.technician !== 'all') {
            filtered = filtered.filter(log => log.technician === filters.technician);
        }

        // Priority filter
        if (filters.priority !== 'all') {
            filtered = filtered.filter(log => log.priority === filters.priority);
        }

        // Date range filter
        if (filters.dateFrom) {
            const fromDate = new Date(filters.dateFrom);
            filtered = filtered.filter(log => new Date(log.date) >= fromDate);
        }

        if (filters.dateTo) {
            const toDate = new Date(filters.dateTo);
            toDate.setHours(23, 59, 59, 999); // End of day
            filtered = filtered.filter(log => new Date(log.date) <= toDate);
        }

        return filtered;
    }, [allLogs, filters]);

    // Sort logs
    const sortedLogs = useMemo(() => {
        return [...filteredLogs].sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            if (sortBy === 'date') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            } else if (sortBy === 'priority') {
                aValue = MAINTENANCE_PRIORITIES[aValue]?.urgency || 0;
                bValue = MAINTENANCE_PRIORITIES[bValue]?.urgency || 0;
            }

            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }, [filteredLogs, sortBy, sortOrder]);

    // Calculate statistics
    const stats = useMemo(() => {
        const totalCost = filteredLogs.reduce((sum, log) => sum + (log.cost || 0), 0);
        const totalHours = filteredLogs.reduce((sum, log) => sum + (log.duration || 0), 0);
        const avgCost = filteredLogs.length > 0 ? totalCost / filteredLogs.length : 0;
        const avgDuration = filteredLogs.length > 0 ? totalHours / filteredLogs.length : 0;

        const priorityBreakdown = Object.keys(MAINTENANCE_PRIORITIES).reduce((acc, priority) => {
            acc[priority] = filteredLogs.filter(log => log.priority === priority).length;
            return acc;
        }, {});

        return {
            totalLogs: filteredLogs.length,
            totalCost,
            totalHours,
            avgCost,
            avgDuration,
            priorityBreakdown
        };
    }, [filteredLogs]);

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const clearFilters = () => {
        setFilters({
            search: '',
            machine: 'all',
            technician: 'all',
            priority: 'all',
            dateFrom: '',
            dateTo: ''
        });
    };

    const exportData = () => {
        // Simple CSV export
        const headers = ['Date', 'Machine', 'Technician', 'Description', 'Duration', 'Cost', 'Priority', 'Parts Used'];
        const csvData = [
            headers.join(','),
            ...sortedLogs.map(log => [
                formatDate(log.date),
                log.machineName,
                log.technician,
                `"${log.description}"`,
                log.duration,
                log.cost,
                log.priority,
                `"${log.partsUsed || ''}"`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `maintenance-logs-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Maintenance Logs</h1>
                    <p className="text-gray-600 mt-1">
                        Complete maintenance history across all machines
                    </p>
                </div>
                
                <div className="mt-4 sm:mt-0 flex items-center space-x-3">
                    <button
                        onClick={exportData}
                        className="btn-secondary"
                        disabled={sortedLogs.length === 0}
                    >
                        Export CSV
                    </button>
                </div>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <div className="stat-card">
                    <div className="stat-value text-gray-900">{stats.totalLogs}</div>
                    <div className="stat-label">Total Logs</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-green-600">{formatCurrency(stats.totalCost)}</div>
                    <div className="stat-label">Total Cost</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-blue-600">{stats.totalHours.toFixed(1)}h</div>
                    <div className="stat-label">Total Hours</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-purple-600">{formatCurrency(stats.avgCost)}</div>
                    <div className="stat-label">Avg Cost</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-orange-600">{stats.avgDuration.toFixed(1)}h</div>
                    <div className="stat-label">Avg Duration</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-red-600">{stats.priorityBreakdown.critical || 0}</div>
                    <div className="stat-label">Critical Issues</div>
                </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Filters</h3>
                    <button
                        onClick={clearFilters}
                        className="text-sm text-medical-blue-600 hover:text-medical-blue-800"
                    >
                        Clear All
                    </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Search
                        </label>
                        <input
                            type="text"
                            placeholder="Search logs..."
                            value={filters.search}
                            onChange={(e) => handleFilterChange('search', e.target.value)}
                            className="input-field"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Machine
                        </label>
                        <select
                            value={filters.machine}
                            onChange={(e) => handleFilterChange('machine', e.target.value)}
                            className="input-field"
                        >
                            <option value="all">All Machines</option>
                            {uniqueMachines.map(machine => (
                                <option key={machine.id} value={machine.id}>
                                    {machine.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Technician
                        </label>
                        <select
                            value={filters.technician}
                            onChange={(e) => handleFilterChange('technician', e.target.value)}
                            className="input-field"
                        >
                            <option value="all">All Technicians</option>
                            {uniqueTechnicians.map(technician => (
                                <option key={technician} value={technician}>
                                    {technician}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Priority
                        </label>
                        <select
                            value={filters.priority}
                            onChange={(e) => handleFilterChange('priority', e.target.value)}
                            className="input-field"
                        >
                            <option value="all">All Priorities</option>
                            {Object.entries(MAINTENANCE_PRIORITIES).map(([key, priority]) => (
                                <option key={key} value={key}>
                                    {priority.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            From Date
                        </label>
                        <input
                            type="date"
                            value={filters.dateFrom}
                            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                            className="input-field"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            To Date
                        </label>
                        <input
                            type="date"
                            value={filters.dateTo}
                            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                            className="input-field"
                        />
                    </div>
                </div>
            </div>

            {/* Sort Controls */}
            <div className="bg-white rounded-lg shadow p-4">
                <div className="flex items-center space-x-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Sort by
                        </label>
                        <select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value)}
                            className="input-field"
                        >
                            <option value="date">Date</option>
                            <option value="machineName">Machine</option>
                            <option value="technician">Technician</option>
                            <option value="priority">Priority</option>
                            <option value="cost">Cost</option>
                            <option value="duration">Duration</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Order
                        </label>
                        <select
                            value={sortOrder}
                            onChange={(e) => setSortOrder(e.target.value)}
                            className="input-field"
                        >
                            <option value="desc">Descending</option>
                            <option value="asc">Ascending</option>
                        </select>
                    </div>
                    <div className="flex-1 text-right">
                        <p className="text-sm text-gray-600 mt-6">
                            Showing {sortedLogs.length} of {allLogs.length} logs
                        </p>
                    </div>
                </div>
            </div>

            {/* Maintenance Logs */}
            {sortedLogs.length > 0 ? (
                <div className="bg-white rounded-lg shadow overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Machine
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Description
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Technician
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Priority
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Duration
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Cost
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {sortedLogs.map(log => (
                                    <tr key={`${log.machineId}-${log.id}`} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {formatDate(log.date)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    {log.machineName}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {log.machineLocation}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs">
                                            <div className="truncate" title={log.description}>
                                                {log.description}
                                            </div>
                                            {log.partsUsed && (
                                                <div className="text-xs text-gray-500 mt-1">
                                                    Parts: {log.partsUsed}
                                                </div>
                                            )}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {log.technician}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className={`priority-badge priority-${log.priority}`}>
                                                {MAINTENANCE_PRIORITIES[log.priority]?.label || log.priority}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {log.duration}h
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {formatCurrency(log.cost)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <Link
                                                to={`/machine/${log.machineId}`}
                                                className="text-medical-blue-600 hover:text-medical-blue-900"
                                            >
                                                View Machine
                                            </Link>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 text-6xl mb-4">📋</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No maintenance logs found</h3>
                    <p className="text-gray-600">
                        Try adjusting your search criteria or filters.
                    </p>
                </div>
            )}
        </div>
    );
};

export default MaintenanceLogScreen;
