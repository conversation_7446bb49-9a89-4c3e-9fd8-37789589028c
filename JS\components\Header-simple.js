// DialysisCare - Simple Header Component for Testing
// Minimal header for debugging

import React from 'react';

const Header = () => {
    console.log('Header component rendering...');
    
    return (
        <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <div className="flex items-center">
                        <h1 className="text-xl font-bold text-gray-900">
                            DialysisCare
                        </h1>
                    </div>
                    
                    <nav className="flex space-x-4">
                        <a href="#" className="text-gray-600 hover:text-gray-900">Dashboard</a>
                        <a href="#" className="text-gray-600 hover:text-gray-900">Machines</a>
                        <a href="#" className="text-gray-600 hover:text-gray-900">Settings</a>
                    </nav>
                </div>
            </div>
        </header>
    );
};

export default Header;
