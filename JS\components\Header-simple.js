// DialysisCare - Header Component with Connection Status
// Header with offline/online awareness and working navigation

import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Header = () => {
    console.log('Header component rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const location = useLocation();

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    return (
        <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <div className="flex items-center">
                        <div className="flex items-center">
                            <h1 className="text-xl font-bold text-gray-900 mr-3">
                                DialysisCare
                            </h1>

                            {/* Connection Status Indicator */}
                            <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                isOnline
                                    ? 'bg-green-100 text-green-700'
                                    : 'bg-red-100 text-red-700'
                            }`}>
                                <div className={`w-1.5 h-1.5 rounded-full mr-1 ${
                                    isOnline ? 'bg-green-500' : 'bg-red-500'
                                }`}></div>
                                {isOnline ? 'Online' : 'Offline'}
                            </div>
                        </div>
                    </div>

                    <nav className="flex space-x-4">
                        <Link
                            to="/"
                            className={`transition-colors font-medium ${
                                location.pathname === '/'
                                    ? 'text-blue-600 border-b-2 border-blue-600 pb-1'
                                    : isOnline
                                        ? 'text-gray-600 hover:text-gray-900'
                                        : 'text-gray-400 cursor-not-allowed'
                            }`}
                            onClick={(e) => !isOnline && e.preventDefault()}
                        >
                            Dashboard
                        </Link>

                        <Link
                            to="/machines"
                            className={`transition-colors font-medium ${
                                location.pathname === '/machines'
                                    ? 'text-blue-600 border-b-2 border-blue-600 pb-1'
                                    : isOnline
                                        ? 'text-gray-600 hover:text-gray-900'
                                        : 'text-gray-400 cursor-not-allowed'
                            }`}
                            onClick={(e) => !isOnline && e.preventDefault()}
                            title={!isOnline ? 'This feature requires an internet connection' : ''}
                        >
                            Machines
                        </Link>

                        <Link
                            to="/settings"
                            className={`transition-colors font-medium ${
                                location.pathname === '/settings'
                                    ? 'text-blue-600 border-b-2 border-blue-600 pb-1'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            Settings
                        </Link>
                    </nav>
                </div>
            </div>
        </header>
    );
};

export default Header;
