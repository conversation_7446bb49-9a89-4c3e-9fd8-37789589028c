// DialysisCare - Header Component with Connection Status
// Header with offline/online awareness

import React, { useState, useEffect } from 'react';

const Header = () => {
    console.log('Header component rendering...');
    const [isOnline, setIsOnline] = useState(navigator.onLine);

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    return (
        <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <div className="flex items-center">
                        <div className="flex items-center">
                            <h1 className="text-xl font-bold text-gray-900 mr-3">
                                DialysisCare
                            </h1>

                            {/* Connection Status Indicator */}
                            <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                isOnline
                                    ? 'bg-green-100 text-green-700'
                                    : 'bg-red-100 text-red-700'
                            }`}>
                                <div className={`w-1.5 h-1.5 rounded-full mr-1 ${
                                    isOnline ? 'bg-green-500' : 'bg-red-500'
                                }`}></div>
                                {isOnline ? 'Online' : 'Offline'}
                            </div>
                        </div>
                    </div>

                    <nav className="flex space-x-4">
                        <a href="#" className={`transition-colors ${
                            isOnline
                                ? 'text-gray-600 hover:text-gray-900'
                                : 'text-gray-400 cursor-not-allowed'
                        }`}>
                            Dashboard
                        </a>
                        <a href="#" className={`transition-colors ${
                            isOnline
                                ? 'text-gray-600 hover:text-gray-900'
                                : 'text-gray-400 cursor-not-allowed'
                        }`}>
                            Machines
                        </a>
                        <a href="#" className="text-gray-600 hover:text-gray-900">
                            Settings
                        </a>
                    </nav>
                </div>
            </div>
        </header>
    );
};

export default Header;
