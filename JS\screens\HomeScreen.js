// DialysisCare - Home Screen Component
// Main dashboard showing machine overview and status

import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext.js';
import { useNotification } from '../context/NotificationContext.js';
import {
    getStatusColor,
    getPriorityColor,
    formatDate,
    getDaysUntilMaintenance,
    getMaintenanceStatus
} from '../constants.js';

const HomeScreen = () => {
    const { machines, loading, error, filters, setFilters, selectMachine } = useApp();
    const { showSuccess, showError } = useNotification();
    const [viewMode, setViewMode] = useState('grid');

    // Filter and sort machines
    const filteredMachines = useMemo(() => {
        let filtered = machines;

        // Apply status filter
        if (filters.status !== 'all') {
            filtered = filtered.filter(machine => machine.status === filters.status);
        }

        // Apply location filter
        if (filters.location !== 'all') {
            filtered = filtered.filter(machine => machine.location === filters.location);
        }

        // Apply search filter
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filtered = filtered.filter(machine =>
                machine.name.toLowerCase().includes(searchTerm) ||
                machine.manufacturer.toLowerCase().includes(searchTerm) ||
                machine.serialNumber.toLowerCase().includes(searchTerm) ||
                machine.location.toLowerCase().includes(searchTerm)
            );
        }

        return filtered;
    }, [machines, filters]);

    // Calculate statistics
    const stats = useMemo(() => {
        const total = machines.length;
        const operational = machines.filter(m => m.status === 'operational').length;
        const maintenance = machines.filter(m => m.status === 'maintenance').length;
        const offline = machines.filter(m => m.status === 'offline').length;
        const dueSoon = machines.filter(m => {
            const days = getDaysUntilMaintenance(m.nextMaintenanceDate);
            return days !== null && days <= 7 && days >= 0;
        }).length;
        const overdue = machines.filter(m => {
            const days = getDaysUntilMaintenance(m.nextMaintenanceDate);
            return days !== null && days < 0;
        }).length;

        return { total, operational, maintenance, offline, dueSoon, overdue };
    }, [machines]);

    const handleMachineClick = (machine) => {
        selectMachine(machine);
    };

    const handleQuickAction = (action, machine) => {
        switch (action) {
            case 'maintenance':
                showSuccess(`Maintenance scheduled for ${machine.name}`);
                break;
            case 'repair':
                showSuccess(`Repair request submitted for ${machine.name}`);
                break;
            case 'inspect':
                showSuccess(`Inspection logged for ${machine.name}`);
                break;
            default:
                break;
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-64">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-medical-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading machines...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-red-800 font-medium">Error Loading Data</h3>
                <p className="text-red-600 mt-2">{error}</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                    <p className="text-gray-600 mt-1">
                        Monitor and manage your hemodialysis machines
                    </p>
                </div>
                
                <div className="mt-4 sm:mt-0 flex items-center space-x-3">
                    <button
                        onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                        className="btn-secondary"
                    >
                        {viewMode === 'grid' ? 'List View' : 'Grid View'}
                    </button>
                </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div className="stat-card">
                    <div className="stat-value text-gray-900">{stats.total}</div>
                    <div className="stat-label">Total Machines</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-green-600">{stats.operational}</div>
                    <div className="stat-label">Operational</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-yellow-600">{stats.maintenance}</div>
                    <div className="stat-label">In Maintenance</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-red-600">{stats.offline}</div>
                    <div className="stat-label">Offline</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-orange-600">{stats.dueSoon}</div>
                    <div className="stat-label">Due Soon</div>
                </div>
                <div className="stat-card">
                    <div className="stat-value text-red-700">{stats.overdue}</div>
                    <div className="stat-label">Overdue</div>
                </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Search
                        </label>
                        <input
                            type="text"
                            placeholder="Search machines..."
                            value={filters.search}
                            onChange={(e) => setFilters({ search: e.target.value })}
                            className="input-field"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Status
                        </label>
                        <select
                            value={filters.status}
                            onChange={(e) => setFilters({ status: e.target.value })}
                            className="input-field"
                        >
                            <option value="all">All Statuses</option>
                            <option value="operational">Operational</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="offline">Offline</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Location
                        </label>
                        <select
                            value={filters.location}
                            onChange={(e) => setFilters({ location: e.target.value })}
                            className="input-field"
                        >
                            <option value="all">All Locations</option>
                            {[...new Set(machines.map(m => m.location))].map(location => (
                                <option key={location} value={location}>{location}</option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>

            {/* Machines Grid/List */}
            <div className={`
                ${viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
                    : 'space-y-4'
                }
            `}>
                {filteredMachines.map(machine => (
                    <MachineCard
                        key={machine.id}
                        machine={machine}
                        viewMode={viewMode}
                        onClick={() => handleMachineClick(machine)}
                        onQuickAction={(action) => handleQuickAction(action, machine)}
                    />
                ))}
            </div>

            {filteredMachines.length === 0 && (
                <div className="text-center py-12">
                    <div className="text-gray-400 text-6xl mb-4">🔍</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No machines found</h3>
                    <p className="text-gray-600">
                        Try adjusting your search criteria or filters.
                    </p>
                </div>
            )}
        </div>
    );
};

// Machine Card Component
const MachineCard = ({ machine, viewMode, onClick, onQuickAction }) => {
    const maintenanceStatus = getMaintenanceStatus(machine.nextMaintenanceDate);
    const daysUntilMaintenance = getDaysUntilMaintenance(machine.nextMaintenanceDate);

    if (viewMode === 'list') {
        return (
            <div className="machine-card-list" onClick={onClick}>
                <div className="flex items-center space-x-4">
                    <div className={`status-indicator status-${machine.status}`}></div>
                    <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">{machine.name}</h3>
                        <p className="text-sm text-gray-600">{machine.location}</p>
                    </div>
                    <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                            Next: {formatDate(machine.nextMaintenanceDate)}
                        </div>
                        <div className={`text-xs ${
                            maintenanceStatus === 'overdue' ? 'text-red-600' :
                            maintenanceStatus === 'due-soon' ? 'text-orange-600' :
                            'text-gray-600'
                        }`}>
                            {daysUntilMaintenance !== null && (
                                daysUntilMaintenance < 0 
                                    ? `${Math.abs(daysUntilMaintenance)} days overdue`
                                    : `${daysUntilMaintenance} days remaining`
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="machine-card" onClick={onClick}>
            <div className="machine-card-header">
                <div className="flex items-center justify-between">
                    <h3 className="machine-card-title">{machine.name}</h3>
                    <div className={`status-badge status-${machine.status}`}>
                        {machine.status}
                    </div>
                </div>
                <p className="machine-card-subtitle">{machine.manufacturer}</p>
            </div>

            <div className="machine-card-body">
                <div className="machine-card-info">
                    <div className="info-item">
                        <span className="info-label">Location:</span>
                        <span className="info-value">{machine.location}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Serial:</span>
                        <span className="info-value">{machine.serialNumber}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Last Maintenance:</span>
                        <span className="info-value">{formatDate(machine.lastMaintenanceDate)}</span>
                    </div>
                    <div className="info-item">
                        <span className="info-label">Next Maintenance:</span>
                        <span className={`info-value ${
                            maintenanceStatus === 'overdue' ? 'text-red-600' :
                            maintenanceStatus === 'due-soon' ? 'text-orange-600' :
                            ''
                        }`}>
                            {formatDate(machine.nextMaintenanceDate)}
                            {daysUntilMaintenance !== null && (
                                <span className="block text-xs">
                                    {daysUntilMaintenance < 0 
                                        ? `${Math.abs(daysUntilMaintenance)} days overdue`
                                        : `${daysUntilMaintenance} days remaining`
                                    }
                                </span>
                            )}
                        </span>
                    </div>
                </div>
            </div>

            <div className="machine-card-footer">
                <Link
                    to={`/machine/${machine.id}`}
                    className="btn-primary btn-sm"
                    onClick={(e) => e.stopPropagation()}
                >
                    View Details
                </Link>
            </div>
        </div>
    );
};

export default HomeScreen;
