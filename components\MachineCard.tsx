import React from 'react';
import { Link } from 'react-router-dom';
import { Machine } from '../types';

interface MachineCardProps {
  machine: Machine;
}

const CalendarIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-1.5 text-gray-500"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg>
);

const PinIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-1.5 text-gray-500"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg>
);


const MachineCard: React.FC<MachineCardProps> = ({ machine }) => {
  const formattedDate = machine.lastMaintenanceDate
    ? machine.lastMaintenanceDate.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    : 'Not yet performed';

  return (
    <Link to={`/machine/${machine.id}`} className="block">
      <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 border border-gray-200 overflow-hidden">
        <div className="p-5">
          <div className="flex justify-between items-start">
            <div>
                <h3 className="text-lg font-bold text-brand-blue-800">{machine.model}</h3>
                <p className="text-sm text-gray-500">SN: {machine.serialNumber}</p>
            </div>
            <span className={`px-3 py-1 text-xs font-semibold rounded-full ${machine.lastMaintenanceDate ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                {machine.lastMaintenanceDate ? 'Operational' : 'Needs Check'}
            </span>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200 flex flex-col sm:flex-row sm:justify-between space-y-2 sm:space-y-0 text-sm text-gray-600">
            <div className="flex items-center">
                <PinIcon />
                <span>{machine.location}</span>
            </div>
            <div className="flex items-center">
                <CalendarIcon />
                <span>Last Maintenance: {formattedDate}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default MachineCard;