import React from 'react';
import { useNavigate } from 'react-router-dom';

const ChevronLeftIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2"><path d="m15 18-6-6 6-6"></path></svg>
);


const SettingsScreen: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div>
        <button onClick={() => navigate(-1)} className="inline-flex items-center mb-6 text-sm font-medium text-gray-600 hover:text-brand-blue-700">
          <ChevronLeftIcon />
          Back
      </button>

      <div className="bg-white p-6 sm:p-8 rounded-lg shadow-lg">
        <h2 className="text-2xl font-bold tracking-tight text-gray-900">Settings</h2>
        <div className="mt-6 border-t border-gray-200 pt-6">
          <p className="text-gray-600">
            Coming soon: User Preferences, Data Synchronization, Notifications, etc.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SettingsScreen;