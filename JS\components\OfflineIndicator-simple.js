// DialysisCare - Offline Indicator Component
// Shows offline status and handles connectivity

import React, { useState, useEffect } from 'react';

const OfflineIndicator = () => {
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [showIndicator, setShowIndicator] = useState(!navigator.onLine);

    useEffect(() => {
        console.log('OfflineIndicator: Setting up online/offline listeners');

        const handleOnline = () => {
            console.log('OfflineIndicator: Device is online');
            setIsOnline(true);
            setShowIndicator(false);

            // Hide the HTML offline indicator
            const htmlIndicator = document.getElementById('offline-indicator');
            if (htmlIndicator) {
                htmlIndicator.classList.add('hidden');
            }
        };

        const handleOffline = () => {
            console.log('OfflineIndicator: Device is offline');
            setIsOnline(false);
            setShowIndicator(true);

            // Show the HTML offline indicator
            const htmlIndicator = document.getElementById('offline-indicator');
            if (htmlIndicator) {
                htmlIndicator.classList.remove('hidden');
            }
        };

        // Add event listeners
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        // Initial check
        if (!navigator.onLine) {
            handleOffline();
        } else {
            handleOnline();
        }

        // Cleanup
        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    // React component indicator (in addition to HTML one)
    if (!showIndicator) {
        return null;
    }

    return (
        <div className="fixed bottom-20 left-4 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg z-50">
            <div className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                </svg>
                <span className="text-sm font-medium">
                    You're offline. Some features may be limited.
                </span>
            </div>
        </div>
    );
};

export default OfflineIndicator;
