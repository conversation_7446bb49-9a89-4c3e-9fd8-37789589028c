// DialysisCare - Main Application Component
// Professional Hemodialysis Maintenance Tracker

import React, { useState, useEffect } from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';

// Import screens
import HomeScreen from './screens/HomeScreen.js';
import MachineDetailsScreen from './screens/MachineDetailsScreen.js';
import MaintenanceLogScreen from './screens/MaintenanceLogScreen.js';
import SettingsScreen from './screens/SettingsScreen.js';

// Import components
import Header from './components/Header.js';
import ErrorBoundary from './components/ErrorBoundary.js';
import OfflineIndicator from './components/OfflineIndicator.js';
import ThemeProvider from './components/ThemeProvider.js';

// Import utilities
import { AppProvider } from './context/AppContext.js';
import { NotificationProvider } from './context/NotificationContext.js';

const App = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        // Initialize application
        const initializeApp = async () => {
            try {
                // Simulate initialization delay
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Check for required browser features
                if (!window.localStorage) {
                    throw new Error('LocalStorage is not supported in this browser');
                }
                
                if (!window.fetch) {
                    throw new Error('Fetch API is not supported in this browser');
                }
                
                // Initialize theme
                const savedTheme = localStorage.getItem('dialysiscare-theme') || 'light';
                document.documentElement.setAttribute('data-theme', savedTheme);
                
                setIsLoading(false);
                
            } catch (err) {
                console.error('App initialization error:', err);
                setError(err.message);
                setIsLoading(false);
            }
        };

        initializeApp();
    }, []);

    if (isLoading) {
        return (
            <div className="min-h-screen bg-medical-blue-700 flex items-center justify-center">
                <div className="text-center text-white">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <h2 className="text-xl font-semibold">Loading DialysisCare...</h2>
                    <p className="text-medical-blue-200 mt-2">Initializing maintenance tracking system</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
                <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
                    <div className="flex items-center mb-4">
                        <svg className="w-6 h-6 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <h1 className="text-lg font-semibold text-gray-900">Application Error</h1>
                    </div>
                    <p className="text-gray-600 mb-4">
                        {error}
                    </p>
                    <button 
                        onClick={() => window.location.reload()} 
                        className="w-full bg-medical-blue-600 text-white py-2 px-4 rounded hover:bg-medical-blue-700 transition-colors"
                    >
                        Reload Application
                    </button>
                </div>
            </div>
        );
    }

    return (
        <ErrorBoundary>
            <ThemeProvider>
                <AppProvider>
                    <NotificationProvider>
                        <HashRouter>
                            <div className="min-h-screen bg-gray-50 text-gray-800">
                                <Header />
                                
                                <main className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto">
                                    <Routes>
                                        <Route path="/" element={<HomeScreen />} />
                                        <Route path="/machine/:id" element={<MachineDetailsScreen />} />
                                        <Route path="/machine/:id/log" element={<MaintenanceLogScreen />} />
                                        <Route path="/settings" element={<SettingsScreen />} />
                                        <Route path="*" element={<Navigate to="/" replace />} />
                                    </Routes>
                                </main>
                                
                                <OfflineIndicator />
                            </div>
                        </HashRouter>
                    </NotificationProvider>
                </AppProvider>
            </ThemeProvider>
        </ErrorBoundary>
    );
};

export default App;
