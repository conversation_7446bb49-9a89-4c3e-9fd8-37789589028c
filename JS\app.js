// DialysisCare - Main Application Component
// Professional Hemodialysis Maintenance Tracker

import React, { useState, useEffect } from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';

// Import screens
import HomeScreen from './screens/HomeScreen-simple.js';
import MachineListScreen from './screens/MachineListScreen-simple.js';
import MachineDetailsScreen from './screens/MachineDetailsScreen-simple.js';
import MachineOperatingConsole from './screens/MachineOperatingConsole.js';
import MaintenanceLogScreen from './screens/MaintenanceLogScreen-simple.js';
import SettingsScreen from './screens/SettingsScreen-simple.js';

// Import components
import Header from './components/Header-simple.js';
import ErrorBoundary from './components/ErrorBoundary-simple.js';
import OfflineIndicator from './components/OfflineIndicator-simple.js';
import ThemeProvider from './components/ThemeProvider-simple.js';

// Import utilities
import { AppProvider } from './context/AppContext-simple.js';
import { NotificationProvider } from './context/NotificationContext-simple.js';

const App = () => {
    console.log('App component rendering...');



    return (
        <ErrorBoundary>
            <ThemeProvider>
                <AppProvider>
                    <NotificationProvider>
                        <HashRouter>
                            <div className="min-h-screen bg-gray-50 text-gray-800">
                                <Header />
                                
                                <main className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto">
                                    <Routes>
                                        <Route path="/" element={<HomeScreen />} />
                                        <Route path="/machines" element={<MachineListScreen />} />
                                        <Route path="/machine/:id" element={<MachineDetailsScreen />} />
                    <Route path="/machine/:id/console" element={<MachineOperatingConsole />} />
                                        <Route path="/machine/:id/log" element={<MaintenanceLogScreen />} />
                                        <Route path="/settings" element={<SettingsScreen />} />
                                        <Route path="*" element={<Navigate to="/" replace />} />
                                    </Routes>
                                </main>
                                
                                <OfflineIndicator />
                            </div>
                        </HashRouter>
                    </NotificationProvider>
                </AppProvider>
            </ThemeProvider>
        </ErrorBoundary>
    );
};

export default App;
