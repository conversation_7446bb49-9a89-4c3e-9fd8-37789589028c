// DialysisCare - Machine Operating Console
// Real-time hemodialysis machine monitoring and control interface

import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';

const MachineOperatingConsole = () => {
    const { id } = useParams();
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [currentTime, setCurrentTime] = useState(new Date());
    const [treatmentActive, setTreatmentActive] = useState(false);
    const [treatmentStartTime, setTreatmentStartTime] = useState(null);
    const [alarmActive, setAlarmActive] = useState(false);

    // Standard Hemodialysis Parameters with Normal Ranges
    const [parameters, setParameters] = useState({
        // Blood Circuit Parameters
        bloodFlow: { value: 300, min: 200, max: 450, unit: 'mL/min', status: 'normal' },
        bloodPressure: { 
            arterial: { value: -180, min: -250, max: -100, unit: 'mmHg', status: 'normal' },
            venous: { value: 120, min: 50, max: 200, unit: 'mmHg', status: 'normal' }
        },
        
        // Dialysate Circuit Parameters
        dialysateFlow: { value: 500, min: 300, max: 800, unit: 'mL/min', status: 'normal' },
        dialysateTemp: { value: 36.5, min: 35.5, max: 37.5, unit: '°C', status: 'normal' },
        dialysateConductivity: { value: 14.0, min: 13.0, max: 15.0, unit: 'mS/cm', status: 'normal' },
        
        // Ultrafiltration Parameters
        ultraFiltrationRate: { value: 0.8, min: 0.0, max: 2.0, unit: 'L/hr', status: 'normal' },
        totalUFGoal: { value: 2.5, min: 0.0, max: 5.0, unit: 'L', status: 'normal' },
        currentUFRemoved: { value: 0.6, min: 0.0, max: 5.0, unit: 'L', status: 'normal' },
        
        // Transmembrane Pressure
        tmp: { value: 150, min: 0, max: 300, unit: 'mmHg', status: 'normal' },
        
        // Water Quality Parameters
        waterTemp: { value: 23.5, min: 20.0, max: 25.0, unit: '°C', status: 'normal' },
        waterPressure: { value: 2.8, min: 2.0, max: 4.0, unit: 'bar', status: 'normal' },
        endotoxinLevel: { value: 0.03, min: 0.0, max: 0.25, unit: 'EU/mL', status: 'normal' },
        
        // Machine Status
        treatmentTime: { value: 0, min: 0, max: 300, unit: 'min', status: 'normal' },
        ktv: { value: 1.4, min: 1.2, max: 2.0, unit: '', status: 'normal' }
    });

    // Quality Test Standards (Based on AAMI/ISO Standards)
    const qualityStandards = {
        waterQuality: {
            bacteria: { limit: 100, unit: 'CFU/mL', current: 5 },
            endotoxin: { limit: 0.25, unit: 'EU/mL', current: 0.03 },
            chlorine: { limit: 0.1, unit: 'mg/L', current: 0.02 },
            chloramine: { limit: 0.1, unit: 'mg/L', current: 0.01 }
        },
        dialysateQuality: {
            bacteria: { limit: 100, unit: 'CFU/mL', current: 8 },
            endotoxin: { limit: 0.25, unit: 'EU/mL', current: 0.05 },
            conductivity: { min: 13.0, max: 15.0, unit: 'mS/cm', current: 14.0 },
            temperature: { min: 35.5, max: 37.5, unit: '°C', current: 36.5 }
        }
    };

    // Simulate real-time parameter updates
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date());
            
            if (treatmentActive) {
                setParameters(prev => {
                    const newParams = { ...prev };
                    
                    // Simulate realistic parameter fluctuations
                    newParams.bloodFlow.value += (Math.random() - 0.5) * 10;
                    newParams.dialysateFlow.value += (Math.random() - 0.5) * 20;
                    newParams.dialysateTemp.value += (Math.random() - 0.5) * 0.2;
                    newParams.tmp.value += (Math.random() - 0.5) * 20;
                    newParams.currentUFRemoved.value += 0.01; // Gradual UF removal
                    newParams.treatmentTime.value += 1; // Increment treatment time
                    
                    // Update parameter status based on ranges
                    Object.keys(newParams).forEach(key => {
                        const param = newParams[key];
                        if (param.value !== undefined && param.min !== undefined && param.max !== undefined) {
                            if (param.value < param.min || param.value > param.max) {
                                param.status = 'alarm';
                                setAlarmActive(true);
                            } else if (param.value < param.min * 1.1 || param.value > param.max * 0.9) {
                                param.status = 'warning';
                            } else {
                                param.status = 'normal';
                            }
                        }
                    });
                    
                    return newParams;
                });
            }
        }, 1000);

        return () => clearInterval(interval);
    }, [treatmentActive]);

    // Online/Offline detection
    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    const startTreatment = () => {
        setTreatmentActive(true);
        setTreatmentStartTime(new Date());
        setParameters(prev => ({
            ...prev,
            treatmentTime: { ...prev.treatmentTime, value: 0 }
        }));
    };

    const stopTreatment = () => {
        setTreatmentActive(false);
        setTreatmentStartTime(null);
    };

    const acknowledgeAlarm = () => {
        setAlarmActive(false);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'normal': return 'text-green-600 bg-green-50 border-green-200';
            case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'alarm': return 'text-red-600 bg-red-50 border-red-200';
            default: return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const formatTime = (minutes) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    };

    return (
        <div className="p-6 bg-gray-100 min-h-screen">
            {/* Header */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Machine Operating Console</h1>
                        <p className="text-gray-600">Machine ID: {id} • {currentTime.toLocaleString()}</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                            isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                            {isOnline ? 'Online' : 'Offline'}
                        </div>
                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                            treatmentActive ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                            {treatmentActive ? 'Treatment Active' : 'Standby'}
                        </div>
                        {alarmActive && (
                            <button 
                                onClick={acknowledgeAlarm}
                                className="px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 animate-pulse"
                            >
                                🚨 ACKNOWLEDGE ALARM
                            </button>
                        )}
                    </div>
                </div>
            </div>

            {/* Treatment Control Panel */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 className="text-lg font-semibold mb-4">Treatment Control</h2>
                <div className="flex items-center space-x-4">
                    <button 
                        onClick={startTreatment}
                        disabled={treatmentActive}
                        className={`px-6 py-3 rounded-lg font-medium ${
                            treatmentActive 
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                                : 'bg-green-600 text-white hover:bg-green-700'
                        }`}
                    >
                        ▶ Start Treatment
                    </button>
                    <button 
                        onClick={stopTreatment}
                        disabled={!treatmentActive}
                        className={`px-6 py-3 rounded-lg font-medium ${
                            !treatmentActive 
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                                : 'bg-red-600 text-white hover:bg-red-700'
                        }`}
                    >
                        ⏹ Stop Treatment
                    </button>
                    <div className="text-lg font-mono">
                        Treatment Time: {formatTime(parameters.treatmentTime.value)}
                    </div>
                    {treatmentStartTime && (
                        <div className="text-sm text-gray-600">
                            Started: {treatmentStartTime.toLocaleTimeString()}
                        </div>
                    )}
                </div>
            </div>

            {/* Main Parameters Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-6">
                {/* Blood Circuit */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 text-red-700">🩸 Blood Circuit</h3>
                    <div className="space-y-4">
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.bloodFlow.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Blood Flow Rate</span>
                                <span className="text-xl font-mono">{parameters.bloodFlow.value.toFixed(0)} {parameters.bloodFlow.unit}</span>
                            </div>
                            <div className="text-xs mt-1">Range: {parameters.bloodFlow.min}-{parameters.bloodFlow.max} {parameters.bloodFlow.unit}</div>
                        </div>
                        
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.bloodPressure.arterial.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Arterial Pressure</span>
                                <span className="text-xl font-mono">{parameters.bloodPressure.arterial.value.toFixed(0)} {parameters.bloodPressure.arterial.unit}</span>
                            </div>
                        </div>
                        
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.bloodPressure.venous.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Venous Pressure</span>
                                <span className="text-xl font-mono">{parameters.bloodPressure.venous.value.toFixed(0)} {parameters.bloodPressure.venous.unit}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Dialysate Circuit */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 text-blue-700">💧 Dialysate Circuit</h3>
                    <div className="space-y-4">
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.dialysateFlow.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Dialysate Flow</span>
                                <span className="text-xl font-mono">{parameters.dialysateFlow.value.toFixed(0)} {parameters.dialysateFlow.unit}</span>
                            </div>
                        </div>
                        
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.dialysateTemp.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Temperature</span>
                                <span className="text-xl font-mono">{parameters.dialysateTemp.value.toFixed(1)} {parameters.dialysateTemp.unit}</span>
                            </div>
                        </div>
                        
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.dialysateConductivity.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Conductivity</span>
                                <span className="text-xl font-mono">{parameters.dialysateConductivity.value.toFixed(1)} {parameters.dialysateConductivity.unit}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Ultrafiltration */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 text-purple-700">⚖️ Ultrafiltration</h3>
                    <div className="space-y-4">
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.ultraFiltrationRate.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">UF Rate</span>
                                <span className="text-xl font-mono">{parameters.ultraFiltrationRate.value.toFixed(1)} {parameters.ultraFiltrationRate.unit}</span>
                            </div>
                        </div>
                        
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.currentUFRemoved.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">UF Removed</span>
                                <span className="text-xl font-mono">{parameters.currentUFRemoved.value.toFixed(2)} {parameters.currentUFRemoved.unit}</span>
                            </div>
                            <div className="text-xs mt-1">Goal: {parameters.totalUFGoal.value} {parameters.totalUFGoal.unit}</div>
                        </div>
                        
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.tmp.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">TMP</span>
                                <span className="text-xl font-mono">{parameters.tmp.value.toFixed(0)} {parameters.tmp.unit}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Water Quality & Standards Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                {/* Water Quality Parameters */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 text-cyan-700">🚰 Water Quality</h3>
                    <div className="space-y-4">
                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.waterTemp.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Water Temperature</span>
                                <span className="text-xl font-mono">{parameters.waterTemp.value.toFixed(1)} {parameters.waterTemp.unit}</span>
                            </div>
                        </div>

                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.waterPressure.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Water Pressure</span>
                                <span className="text-xl font-mono">{parameters.waterPressure.value.toFixed(1)} {parameters.waterPressure.unit}</span>
                            </div>
                        </div>

                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.endotoxinLevel.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Endotoxin Level</span>
                                <span className="text-xl font-mono">{parameters.endotoxinLevel.value.toFixed(3)} {parameters.endotoxinLevel.unit}</span>
                            </div>
                            <div className="text-xs mt-1">Limit: &lt; 0.25 EU/mL</div>
                        </div>

                        <div className={`p-3 rounded-lg border ${getStatusColor(parameters.ktv.status)}`}>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Kt/V Ratio</span>
                                <span className="text-xl font-mono">{parameters.ktv.value.toFixed(2)}</span>
                            </div>
                            <div className="text-xs mt-1">Target: 1.2-2.0</div>
                        </div>
                    </div>
                </div>

                {/* Quality Test Standards */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                    <h3 className="text-lg font-semibold mb-4 text-green-700">✅ Quality Standards (AAMI/ISO)</h3>

                    <div className="mb-4">
                        <h4 className="font-medium text-gray-700 mb-2">Water Quality Standards</h4>
                        <div className="space-y-2">
                            {Object.entries(qualityStandards.waterQuality).map(([key, standard]) => (
                                <div key={key} className={`p-2 rounded border ${
                                    standard.current <= standard.limit ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                                }`}>
                                    <div className="flex justify-between text-sm">
                                        <span className="capitalize">{key}</span>
                                        <span className="font-mono">
                                            {standard.current} / {standard.limit} {standard.unit}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div>
                        <h4 className="font-medium text-gray-700 mb-2">Dialysate Quality Standards</h4>
                        <div className="space-y-2">
                            {Object.entries(qualityStandards.dialysateQuality).map(([key, standard]) => {
                                let isWithinRange = true;
                                if (standard.min !== undefined && standard.max !== undefined) {
                                    isWithinRange = standard.current >= standard.min && standard.current <= standard.max;
                                } else if (standard.limit !== undefined) {
                                    isWithinRange = standard.current <= standard.limit;
                                }

                                return (
                                    <div key={key} className={`p-2 rounded border ${
                                        isWithinRange ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                                    }`}>
                                        <div className="flex justify-between text-sm">
                                            <span className="capitalize">{key}</span>
                                            <span className="font-mono">
                                                {standard.current} {standard.unit}
                                                {standard.min !== undefined && standard.max !== undefined
                                                    ? ` (${standard.min}-${standard.max})`
                                                    : ` (≤${standard.limit})`
                                                }
                                            </span>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>

            {/* Action Panel */}
            <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Console Actions</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button className="px-4 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                        📊 View Trends
                    </button>
                    <button className="px-4 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors">
                        🧪 Run Quality Test
                    </button>
                    <button className="px-4 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors">
                        📋 Export Report
                    </button>
                    <button className="px-4 py-3 bg-orange-600 text-white rounded-lg font-medium hover:bg-orange-700 transition-colors">
                        ⚙️ Calibrate
                    </button>
                </div>
            </div>

            {/* Parameter Ranges Reference */}
            <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
                <h3 className="text-lg font-semibold mb-4">📋 Standard Parameter Ranges</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <h4 className="font-medium text-gray-700 mb-2">Blood Circuit</h4>
                        <ul className="text-sm space-y-1">
                            <li>• Blood Flow: 200-450 mL/min</li>
                            <li>• Arterial Pressure: -100 to -250 mmHg</li>
                            <li>• Venous Pressure: 50-200 mmHg</li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-medium text-gray-700 mb-2">Dialysate</h4>
                        <ul className="text-sm space-y-1">
                            <li>• Flow Rate: 300-800 mL/min</li>
                            <li>• Temperature: 35.5-37.5°C</li>
                            <li>• Conductivity: 13.0-15.0 mS/cm</li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-medium text-gray-700 mb-2">Quality Limits</h4>
                        <ul className="text-sm space-y-1">
                            <li>• Bacteria: &lt; 100 CFU/mL</li>
                            <li>• Endotoxin: &lt; 0.25 EU/mL</li>
                            <li>• Kt/V Ratio: 1.2-2.0</li>
                        </ul>
                    </div>
                </div>
            </div>

            {/* Navigation */}
            <div className="mt-6">
                <Link
                    to={`/machine/${id}`}
                    className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors"
                >
                    ← Back to Machine Details
                </Link>
            </div>
        </div>
    );
};

export default MachineOperatingConsole;
