import React, { useState } from 'react';
import { MACHINES } from '../constants';
import MachineCard from '../components/MachineCard';

const PlusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6"><line x1="12" x2="12" y1="5" y2="19"></line><line x1="5" x2="19" y1="12" y2="12"></line></svg>
);


const HomeScreen: React.FC = () => {
  const [machines] = useState(MACHINES);

  const handleAddMachine = () => {
    // Placeholder for adding a new machine
    alert('Functionality to add a new machine would be implemented here.');
  };

  return (
    <div className="relative">
      <h2 className="text-2xl font-bold tracking-tight text-gray-900 mb-6">Machine Overview</h2>
      
      {machines.length > 0 ? (
        <div className="grid grid-cols-1 gap-6">
          {machines.map((machine) => (
            <MachineCard key={machine.id} machine={machine} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900">No Machines Found</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by adding a new machine.</p>
        </div>
      )}

      <button
        onClick={handleAddMachine}
        className="fixed bottom-8 right-8 bg-brand-blue-600 text-white rounded-full p-4 shadow-lg hover:bg-brand-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue-500 transition-transform hover:scale-110"
        aria-label="Add New Machine"
      >
        <PlusIcon />
      </button>
    </div>
  );
};

export default HomeScreen;
